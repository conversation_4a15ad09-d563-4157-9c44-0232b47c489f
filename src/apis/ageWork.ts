import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { AgeWork } from 'src/types/idp';
import type { DataResponse } from 'src/types/data';
import type { AgeWorkQueryParams } from 'src/types/api';

/**
 * Get age works by criteria
 */
export async function getAgeWorksByCriteriaApi(
  criteriaId: number,
  params: AgeWorkQueryParams,
): Promise<AxiosResponse<DataResponse<AgeWork>>> {
  return api.get<DataResponse<AgeWork>>(`/age-work-criteria/${criteriaId}/age-works`, { params });
}

/**
 * Get single age work by ID
 */
export async function getAgeWorkApi(id: number): Promise<AxiosResponse<AgeWork>> {
  return api.get<AgeWork>(`/age-works/${id}`);
}

/**
 * Create new age work
 */
export async function createAgeWorkApi(data: Omit<AgeWork, 'id'>): Promise<AxiosResponse<AgeWork>> {
  return api.post<AgeWork>('/age-works', data);
}

/**
 * Update age work
 */
export async function updateAgeWorkApi(
  id: number,
  data: Partial<AgeWork>,
): Promise<AxiosResponse<AgeWork>> {
  return api.patch<AgeWork>(`/age-works/${id}`, data);
}

/**
 * Delete age work
 */
export async function deleteAgeWorkApi(id: number): Promise<AxiosResponse<void>> {
  return api.delete(`/age-works/${id}`);
}
