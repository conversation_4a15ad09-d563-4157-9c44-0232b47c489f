import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { AgeWorkCriteria, AgeWork } from 'src/types/idp';
import type { DataResponse } from 'src/types/data';
import type { AgeWorkCriteriaQueryParams, AgeWorkQueryParams } from 'src/types/api';

/**
 * Get all age work criteria with pagination and search
 */
export async function getAllAgeWorkCriteriaApi(
  params: AgeWorkCriteriaQueryParams,
): Promise<AxiosResponse<DataResponse<AgeWorkCriteria>>> {
  return api.get<DataResponse<AgeWorkCriteria>>('/age-work-criteria', { params });
}

/**
 * Get single age work criteria by ID
 */
export async function getAgeWorkCriteriaApi(id: number): Promise<AxiosResponse<AgeWorkCriteria>> {
  return api.get<AgeWorkCriteria>(`/age-work-criteria/${id}`);
}

/**
 * Create new age work criteria
 */
export async function createAgeWorkCriteriaApi(
  data: Omit<AgeWorkCriteria, 'id'>,
): Promise<AxiosResponse<AgeWorkCriteria>> {
  return api.post<AgeWorkCriteria>('/age-work-criteria', data);
}

/**
 * Update age work criteria
 */
export async function updateAgeWorkCriteriaApi(
  id: number,
  data: Partial<AgeWorkCriteria>,
): Promise<AxiosResponse<AgeWorkCriteria>> {
  return api.patch<AgeWorkCriteria>(`/age-work-criteria/${id}`, data);
}

/**
 * Delete age work criteria
 */
export async function deleteAgeWorkCriteriaApi(id: number): Promise<AxiosResponse<void>> {
  return api.delete(`/age-work-criteria/${id}`);
}

/**
 * Get age works by criteria
 */
export async function getAgeWorksByCriteriaApi(
  id: number,
  params: AgeWorkQueryParams,
): Promise<AxiosResponse<DataResponse<AgeWork>>> {
  return api.get<DataResponse<AgeWork>>(`/age-work-criteria/${id}/age-works`, { params });
}
