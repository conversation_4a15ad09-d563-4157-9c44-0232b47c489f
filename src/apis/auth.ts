import { api as http } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { LoginResponse, TokenValidationResponse, User } from 'src/types/api';

/**
 * Pure API function for BUU login
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>>
 */
export async function loginBuuApi(
  username: string,
  password: string,
): Promise<AxiosResponse<LoginResponse>> {
  return await http.post<LoginResponse>('/auth/loginBuu', {
    username,
    password,
  });
}

/**
 * Pure API function for regular login
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>>
 */
export async function loginApi(
  username: string,
  password: string,
): Promise<AxiosResponse<LoginResponse>> {
  return await http.post<LoginResponse>('/auth/login', {
    username,
    password,
  });
}

/**
 * Pure API function for logout
 * @returns Promise<AxiosResponse<void>>
 */
export async function logoutApi(): Promise<AxiosResponse<void>> {
  return await http.post<void>('/auth/logout');
}

/**
 * Pure API function for token refresh
 * @param refreshToken - Refresh token
 * @returns Promise<AxiosResponse<LoginResponse>>
 */
export async function refreshTokenApi(refreshToken: string): Promise<AxiosResponse<LoginResponse>> {
  return await http.post<LoginResponse>('/auth/refresh', {
    refresh_token: refreshToken,
  });
}

/**
 * Pure API function for token validation
 * @returns Promise<AxiosResponse<TokenValidationResponse>>
 */
export async function validateTokenApi(): Promise<AxiosResponse<TokenValidationResponse>> {
  return await http.get<TokenValidationResponse>('/auth/validate');
}

/**
 * Pure API function for getting user profile
 * @returns Promise<AxiosResponse<User>>
 */
export async function getUserProfileApi(): Promise<AxiosResponse<User>> {
  return await http.get<User>('/auth/profile');
}
