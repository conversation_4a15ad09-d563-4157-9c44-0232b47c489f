import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { Competency } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import type { CompetencyQueryParams } from 'src/types/api';

/**
 * Get all competencies with pagination and search
 */
export async function getAllCompetenciesApi(
  path: string,
  params: CompetencyQueryParams,
): Promise<AxiosResponse<DataResponse<Competency>>> {
  return api.get<DataResponse<Competency>>(path, { params });
}

/**
 * Get single competency by ID
 */
export async function getCompetencyApi(
  path: string,
  id: number,
): Promise<AxiosResponse<Competency>> {
  return api.get<Competency>(`${path}/${id}`);
}

/**
 * Get competencies by career type
 */
export async function getCompetenciesByTypeApi(
  path: string,
  careerType: string,
): Promise<AxiosResponse<Competency[]>> {
  return api.get<Competency[]>(`${path}/${careerType}`);
}

/**
 * Create new competency
 */
export async function createCompetencyApi(
  path: string,
  data: Competency,
): Promise<AxiosResponse<Competency>> {
  // Convert data to FormData
  const formData = new FormData();
  formData.append('name', data.name);
  if (data.description) formData.append('description', data.description);
  formData.append('career_type', data.career_type);

  return api.post<Competency>(path, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * Update competency
 */
export async function updateCompetencyApi(
  path: string,
  id: number,
  data: Partial<Competency>,
): Promise<AxiosResponse<Competency>> {
  // Convert data to FormData
  const formData = new FormData();
  if (data.name) formData.append('name', data.name);
  if (data.description) formData.append('description', data.description);
  if (data.career_type) formData.append('career_type', data.career_type);

  return api.patch<Competency>(`${path}/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * Delete competency
 */
export async function deleteCompetencyApi(path: string, id: number): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/${id}`);
}
