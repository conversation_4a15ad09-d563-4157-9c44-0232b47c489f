import { api as axios } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { DevelopmentPlan } from 'src/types/idp';
import type { DevelopmentPlanResponse, DevelopmentPlanQueryParams } from 'src/types/api';

// Development Plan API functions
export const getAllDevPlansApi = async (
  params: DevelopmentPlanQueryParams,
): Promise<AxiosResponse<DevelopmentPlanResponse>> => {
  return axios.get<DevelopmentPlanResponse>('/development-plans', { params });
};

export const getDevPlanApi = async (id: number): Promise<AxiosResponse<DevelopmentPlan>> => {
  return axios.get<DevelopmentPlan>(`/development-plans/${id}`, { params: { isCentral: true } });
};

export const createDevPlanApi = async (
  data: Omit<DevelopmentPlan, 'id'>,
): Promise<AxiosResponse<DevelopmentPlan>> => {
  return axios.post<DevelopmentPlan>('/development-plans', data);
};

export const updateDevPlanApi = async (
  id: number,
  data: Partial<DevelopmentPlan>,
): Promise<AxiosResponse<DevelopmentPlan>> => {
  return axios.patch<DevelopmentPlan>(`/development-plans/${id}`, data);
};

export const deleteDevPlanApi = async (id: number): Promise<AxiosResponse<void>> => {
  return axios.delete(`/development-plans/${id}`);
};
