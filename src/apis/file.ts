import { api as http } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type {
  UploadFileResponse,
  FileListRequest,
  FileListResponse,
  GetPublicFileRequest,
  GetPublicFileResponse,
  GetPublicFilesRequest,
  GetPublicFilesResponse,
  DeleteFileRequest,
  DeleteFileResponse,
  DsPrefixResponse,
  GetPublicFilePathRequest,
  GetPublicFilePathResponse,
} from 'src/types/api';

/**
 * Pure API function for file upload
 * @param filePath - Target path for the file
 * @param fileName - Name of the file
 * @param fileType - MIME type of the file
 * @param files - Array of files to upload
 * @returns Promise<AxiosResponse<UploadFileResponse>>
 */
export async function uploadFileApi(
  filePath: string,
  fileName: string,
  fileType: string,
  files: File[],
): Promise<AxiosResponse<UploadFileResponse>> {
  const formData = new FormData();
  formData.append('path', filePath);
  formData.append('fileName', fileName);
  formData.append('fileType', fileType);
  files.forEach((file) => {
    formData.append('files[]', file);
  });

  return await http.post<UploadFileResponse>('/api/uploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * Pure API function for getting file list
 * @param path - Directory path to list files from
 * @returns Promise<AxiosResponse<FileListResponse>>
 */
export async function getFileListApi(path: string): Promise<AxiosResponse<FileListResponse>> {
  return await http.post<FileListResponse>('/api/getFileList', { path } as FileListRequest);
}

/**
 * Pure API function for getting a public file
 * @param fileName - Name of the file to retrieve
 * @returns Promise<AxiosResponse<GetPublicFileResponse>>
 */
export async function getPublicFileApi(
  fileName: string,
): Promise<AxiosResponse<GetPublicFileResponse>> {
  return await http.post<GetPublicFileResponse>('/api/getPublicFile', {
    fileName,
  } as GetPublicFileRequest);
}

/**
 * Pure API function for getting multiple public files
 * @param files - Array of file objects with fileName
 * @returns Promise<AxiosResponse<GetPublicFilesResponse>>
 */
export async function getPublicFilesApi(
  files: { fileName: string }[],
): Promise<AxiosResponse<GetPublicFilesResponse>> {
  return await http.post<GetPublicFilesResponse>('/api/getPublicFiles', {
    files,
  } as GetPublicFilesRequest);
}

/**
 * Pure API function for deleting a file
 * @param fileName - Name of the file to delete
 * @returns Promise<AxiosResponse<DeleteFileResponse>>
 */
export async function deleteFileApi(fileName: string): Promise<AxiosResponse<DeleteFileResponse>> {
  return await http.delete<DeleteFileResponse>('/api/deleteFile', {
    data: { fileName } as DeleteFileRequest,
  });
}

/**
 * Pure API function for getting DS prefix
 * @returns Promise<AxiosResponse<DsPrefixResponse>>
 */
export async function dsPrefixApi(): Promise<AxiosResponse<DsPrefixResponse>> {
  return await http.get<DsPrefixResponse>('/api/dsPrefix');
}

/**
 * Pure API function for getting public file path
 * @param imagePath - Path of the image
 * @returns Promise<AxiosResponse<GetPublicFilePathResponse>>
 */
export async function getPublicFilePathApi(
  imagePath: string,
): Promise<AxiosResponse<GetPublicFilePathResponse>> {
  return await http.post<GetPublicFilePathResponse>('/file-upload/public-file-path', {
    imagePath,
  } as GetPublicFilePathRequest);
}
