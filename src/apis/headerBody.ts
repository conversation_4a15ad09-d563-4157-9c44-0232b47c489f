import { api as axios } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { HeaderBody } from 'src/types/models';

// Header Body API functions
export const createHeaderBodyApi = async (
  path: string,
  data: { itemBlockId: number; title: string; description: string },
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.post<HeaderBody>(`${path}/`, data);
};

export const fetchAllHeaderBodiesApi = async (
  path: string,
): Promise<AxiosResponse<HeaderBody[]>> => {
  return axios.get<HeaderBody[]>(`${path}/`);
};

export const fetchHeaderBodyApi = async (
  path: string,
  id: number,
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.get<HeaderBody>(`${path}/${id}`);
};

export const updateHeaderBodyApi = async (
  path: string,
  id: number,
  params: Partial<HeaderBody>,
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.patch<HeaderBody>(`${path}/${id}`, params);
};

export const deleteHeaderBodyApi = async (
  path: string,
  id: number,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`${path}/${id}`);
};
