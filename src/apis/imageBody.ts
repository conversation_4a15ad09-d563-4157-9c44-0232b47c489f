import { api as axios } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { ImageBody } from 'src/types/models';

// Image Body API functions
export const createImageBodyApi = async (
  path: string,
  formData: FormData,
): Promise<AxiosResponse<ImageBody>> => {
  return axios.post<ImageBody>(path, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

export const fetchAllImageBodiesApi = async (path: string): Promise<AxiosResponse<ImageBody[]>> => {
  return axios.get<ImageBody[]>(path);
};

export const fetchImageBodyApi = async (
  path: string,
  id: number,
): Promise<AxiosResponse<ImageBody>> => {
  return axios.get<ImageBody>(`${path}/${id}`);
};

export const updateImageBodyApi = async (
  path: string,
  id: number,
  formData: FormData,
): Promise<AxiosResponse<ImageBody>> => {
  return axios.patch<ImageBody>(`${path}/${id}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

export const deleteImageBodyApi = async (
  path: string,
  id: number,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`${path}/${id}`);
};
