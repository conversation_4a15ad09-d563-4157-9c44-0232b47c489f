import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { ItemBlock } from 'src/types/models';

/**
 * Get all item blocks by assessment ID
 */
export async function getAllItemBlocksByAssessmentApi(
  path: string,
  assessmentId: number,
): Promise<AxiosResponse<ItemBlock[]>> {
  return api.get<ItemBlock[]>(path, {
    params: { assessmentId },
  });
}

/**
 * Create a new item block
 */
export async function createItemBlockApi(
  path: string,
  params: ItemBlock,
): Promise<AxiosResponse<ItemBlock>> {
  return api.post<ItemBlock>(path, { params });
}

/**
 * Update item block field
 */
export async function updateItemBlockFieldApi(
  path: string,
  itemId: number,
  params: ItemBlock,
): Promise<AxiosResponse<ItemBlock>> {
  return api.patch<ItemBlock>(`${path}/${itemId}`, { params });
}

/**
 * Delete item block
 */
export async function deleteItemBlockApi(
  path: string,
  itemId: number,
): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/${itemId}`);
}

/**
 * Get item block by ID
 */
export async function getItemBlockByIdApi(
  path: string,
  itemBlockId: number,
): Promise<AxiosResponse<ItemBlock>> {
  return api.get<ItemBlock>(`${path}/${itemBlockId}`);
}

/**
 * Update item block dimensions
 */
export async function updateItemBlockDimensionsApi(
  path: string,
  itemBlockId: number,
  dimensions: { width: number; height: number },
): Promise<AxiosResponse<ItemBlock>> {
  return api.patch(`${path}/${itemBlockId}/dimensions`, dimensions);
}
