import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { Option } from 'src/types/models';

// Define types for option creation and update
export interface CreateOptionData {
  optionText: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export interface UpdateOptionData {
  optionText?: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number | null; // Allow null to explicitly clear the value
  sequence?: number; // Add sequence field for drag-and-drop reordering
  [key: string]: unknown; // Add index signature for FormData compatibility
}

/**
 * Helper function to convert data to FormData for multipart/form-data requests
 */
function toFormData(data: Record<string, unknown>, file?: File): FormData {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value === 'object' && value !== null && !(value instanceof File)) {
        formData.append(key, JSON.stringify(value));
      } else if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean'
      ) {
        formData.append(key, String(value));
      } else {
        formData.append(key, JSON.stringify(value));
      }
    }
  });

  if (file) {
    formData.append('file', file);
  }

  return formData;
}

/**
 * Create option using POST /options
 */
export async function createOptionApi(
  path: string,
  data: CreateOptionData,
  file?: File,
): Promise<AxiosResponse<Option>> {
  const formData = toFormData(data, file);
  return api.post<Option>(path, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Create option for a specific question
 */
export async function createOptionForQuestionApi(
  path: string,
  questionId: number,
  data: CreateOptionData,
  file?: File,
): Promise<AxiosResponse<Option>> {
  const formData = toFormData(data, file);
  return api.post<Option>(`${path}/${questionId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Create option for question with FormData
 */
export async function createOptionForQuestionFormDataApi(
  questionId: number,
  formData: FormData,
): Promise<AxiosResponse<Option>> {
  return api.post<Option>(`/options/${questionId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Get all options
 */
export async function getAllOptionsApi(path: string): Promise<AxiosResponse<Option[]>> {
  return api.get<Option[]>(path);
}

/**
 * Get option by ID
 */
export async function getOptionByIdApi(path: string, id: number): Promise<AxiosResponse<Option>> {
  return api.get<Option>(`${path}/${id}`);
}

/**
 * Update option by ID
 */
export async function updateOptionApi(
  path: string,
  optionId: number,
  data: UpdateOptionData,
  file?: File,
): Promise<AxiosResponse<Option>> {
  const formData = toFormData(data, file);
  return api.patch<Option>(`${path}/${optionId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Update option for a specific question
 */
export async function updateOptionForQuestionApi(
  path: string,
  questionId: number,
  optionId: number,
  data: UpdateOptionData,
  file?: File,
): Promise<AxiosResponse<Option>> {
  const formData = toFormData(data, file);
  return api.patch<Option>(`${path}/${questionId}/${optionId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Update option silently (for auto-save)
 */
export async function updateOptionSilentApi(
  path: string,
  optionId: number,
  data: UpdateOptionData,
  file?: File,
): Promise<AxiosResponse<Option>> {
  // If we have a file or no nextSection null value, use FormData
  if (file || data.nextSection !== null) {
    const formData = toFormData(data, file);
    return api.patch<Option>(`${path}/${optionId}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  } else {
    // For nextSection null updates, use JSON to properly handle null values
    return api.patch<Option>(`${path}/${optionId}`, data, {
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * Find image path by option text
 */
export async function findImagePathApi(
  path: string,
  optionText: string,
): Promise<AxiosResponse<{ imagePath: string }>> {
  return api.get<{ imagePath: string }>(`${path}/file/${optionText}`);
}

/**
 * Upload option with file
 */
export async function uploadOptionWithFileApi(
  path: string,
  option: Option,
  file?: File,
): Promise<AxiosResponse<Option>> {
  const formData = new FormData();

  formData.append('optionText', option.optionText ?? '');
  formData.append('value', option.value?.toString() ?? '0');
  formData.append('sequence', option.sequence?.toString() ?? '1');
  formData.append('itemBlockId', option.itemBlockId?.toString() ?? '0');

  if (file) {
    formData.append('imagePath', file);
  }

  return api.post<Option>(path, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Delete option by ID
 */
export async function deleteOptionApi(path: string, id: number): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/${id}`);
}

/**
 * Delete file by option text
 */
export async function deleteFileByOptionTextApi(
  path: string,
  optionText: string,
): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/deleteFile/${optionText}`);
}
