import type { QTableProps } from 'quasar';
import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { Permission } from 'src/types/models';
import { formatParams } from 'src/utils/formatter';

// API Functions
const PERMISSIONS_PATH = 'permissions';

/**
 * Create a new permission
 */
export async function createPermissionApi(dto: Permission): Promise<AxiosResponse<Permission>> {
  return api.post<Permission>(PERMISSIONS_PATH, dto);
}

/**
 * Get permission by ID
 */
export async function getPermissionByIdApi(id: number): Promise<AxiosResponse<Permission>> {
  return api.get<Permission>(`${PERMISSIONS_PATH}/${id}`);
}

/**
 * Update permission
 */
export async function updatePermissionApi(
  id: number,
  data: Partial<Permission>,
): Promise<AxiosResponse<Permission>> {
  return api.patch<Permission>(`${PERMISSIONS_PATH}/${id}`, data);
}

/**
 * Delete permission
 */
export async function deletePermissionApi(id: number): Promise<AxiosResponse<Permission>> {
  return api.delete<Permission>(`${PERMISSIONS_PATH}/${id}`);
}

/**
 * Get all permissions with pagination
 */
export async function getPermissionsApi(
  pagination: QTableProps['pagination'],
): Promise<AxiosResponse<DataResponse<Permission>>> {
  const params = formatParams(pagination);
  return api.get<DataResponse<Permission>>(PERMISSIONS_PATH, { params });
}
