import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { Question, Option } from 'src/types/models';

/**
 * Create new question with FormData
 */
export async function createQuestionApi(
  questionData: Partial<Question>,
): Promise<AxiosResponse<Question>> {
  const formData = new FormData();
  Object.entries(questionData).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, String(value));
    }
  });

  return api.post<Question>('/questions', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Get questions by quiz ID
 */
export async function getQuestionsApi(quizId?: number): Promise<AxiosResponse<Question[]>> {
  return api.get<Question[]>('/questions', { params: { quizId } });
}

/**
 * Get single question by ID
 */
export async function getQuestionApi(id: number): Promise<AxiosResponse<Question>> {
  return api.get<Question>(`/questions/${id}`);
}

/**
 * Update question
 */
export async function updateQuestionApi(
  id: number,
  data: Partial<Question>,
): Promise<AxiosResponse<Question>> {
  return api.patch<Question>(`/questions/${id}`, data);
}

/**
 * Delete question
 */
export async function deleteQuestionApi(id: number): Promise<AxiosResponse<void>> {
  return api.delete(`/questions/${id}`);
}

/**
 * Create option for question
 */
export async function createOptionApi(
  questionId: number,
  optionData: Partial<Option>,
): Promise<AxiosResponse<Option>> {
  return api.post<Option>(`/questions/${questionId}/options`, optionData);
}

/**
 * Update option
 */
export async function updateOptionApi(
  optionId: number,
  data: Partial<Option>,
): Promise<AxiosResponse<Option>> {
  return api.patch<Option>(`/options/${optionId}`, data);
}

/**
 * Delete option
 */
export async function deleteOptionApi(optionId: number): Promise<AxiosResponse<void>> {
  return api.delete(`/options/${optionId}`);
}

/**
 * Reorder questions in a quiz
 */
export async function reorderQuestionsApi(
  quizId: number,
  questionOrders: Array<{ questionId: number; sequence: number }>,
): Promise<AxiosResponse<Question[]>> {
  return api.patch<Question[]>(`/questions/quiz/${quizId}/reorder`, questionOrders);
}

/**
 * Update question with FormData
 */
export async function updateQuestionWithFormDataApi(
  id: number,
  questionData: Partial<Question>,
): Promise<AxiosResponse<Question>> {
  const formData = new FormData();
  Object.entries(questionData).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, String(value));
    }
  });

  return api.patch<Question>(`/questions/${id}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * Update question field
 */
export async function updateQuestionFieldApi(
  id: number,
  field: string,
  value: unknown,
): Promise<AxiosResponse<Question>> {
  return api.patch<Question>(`/questions/${id}/field`, { field, value });
}

/**
 * Update choice field
 */
export async function updateChoiceFieldApi(
  questionId: number,
  choiceId: number,
  field: string,
  value: unknown,
): Promise<AxiosResponse<Option>> {
  return api.patch<Option>(`/questions/${questionId}/choice/${choiceId}`, { field, value });
}

/**
 * Add choice to question
 */
export async function addChoiceApi(
  questionId: number,
  field: string,
  value: unknown,
): Promise<AxiosResponse<Option>> {
  return api.post<Option>(`/questions/${questionId}/choice`, { field, value });
}

/**
 * Remove choice from question
 */
export async function removeChoiceApi(
  questionId: number,
  choiceId: number,
): Promise<AxiosResponse<void>> {
  return api.delete(`/questions/${questionId}/choice/${choiceId}`);
}
