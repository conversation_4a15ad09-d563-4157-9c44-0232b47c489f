import { api as axios } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { Submission, Response, getQuestionMeta } from 'src/types/models';
import type {
  QuizAllResponsesData,
  QuizMetaResponse,
  QuizScore,
  SaveQuizResponseRequest,
  StartQuizRequest,
  QuizHeaderWithSubmissions,
} from 'src/types/quiz';

// Quiz Meta API functions
export const getQuizMetaResponseApi = async (
  quizId: number,
): Promise<AxiosResponse<QuizMetaResponse>> => {
  return axios.get<QuizMetaResponse>(`/quiz/assessments/${quizId}/meta`);
};

export const getAllQuizResponsesApi = async (
  quizId: number,
): Promise<AxiosResponse<QuizAllResponsesData>> => {
  return axios.get<QuizAllResponsesData>(`/quiz/assessments/${quizId}/response`);
};

// Quiz Submission API functions
export const startQuizApi = async (
  path: string,
  request: StartQuizRequest,
): Promise<AxiosResponse<Submission>> => {
  return axios.post<Submission>(`${path}/start-assessment`, request);
};

export const findSubmissionApi = async (
  path: string,
  linkUrl: string,
  userId: number,
): Promise<AxiosResponse<Submission>> => {
  return axios.get<Submission>(`${path}/find-submission/${linkUrl}/${userId}`);
};

export const submitAssessmentApi = async (
  path: string,
  submissionId: number,
): Promise<AxiosResponse<Submission>> => {
  return axios.patch<Submission>(`${path}/submit-assessment/${submissionId}`);
};

export const getQuizScoreApi = async (
  path: string,
  submissionId: number,
): Promise<AxiosResponse<QuizScore>> => {
  return axios.get<QuizScore>(`${path}/quiz/score/${submissionId}`);
};

// Quiz Question API functions
export const getQuestionApi = async (
  itemBlockPath: string,
  submissionId: number,
  sequence: number,
): Promise<AxiosResponse<getQuestionMeta>> => {
  return axios.get<getQuestionMeta>(`${itemBlockPath}/quiz/sequence/${submissionId}/${sequence}`);
};

// Quiz Response API functions
export const saveQuizResponseApi = async (
  responsePath: string,
  request: SaveQuizResponseRequest,
): Promise<AxiosResponse<Response>> => {
  return axios.post<Response>(`${responsePath}/quiz/save-response`, request);
};

export const deleteQuizResponseApi = async (
  responsePath: string,
  responseId: number,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`${responsePath}/${responseId}`);
};

// Quiz Header API functions
export const getQuizHeaderWithSubmissionsApi = async (
  linkUrl: string,
  userId?: number,
): Promise<AxiosResponse<QuizHeaderWithSubmissions>> => {
  const params = userId ? { userId } : {};
  return axios.get<QuizHeaderWithSubmissions>(
    `/assessments/header-with-submissions/url/${linkUrl}`,
    { params },
  );
};
