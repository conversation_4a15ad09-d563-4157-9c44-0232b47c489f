import { api as axios } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type {
  AssessmentMeta,
  QuestionResponseData,
  ParticipantData,
  ParticipantDetails,
} from 'src/types/quiz-dashboard';
import type { DataParams, DataResponse } from 'src/types/data';

// Quiz Dashboard API functions
export const getDashboardMetaApi = async (
  basePath: string,
  assessmentId: number,
): Promise<AxiosResponse<AssessmentMeta>> => {
  return axios.get<AssessmentMeta>(`${basePath}/${assessmentId}`);
};

export const getQuestionResponsesApi = async (
  basePath: string,
  assessmentId: number,
  params: DataParams,
): Promise<AxiosResponse<DataResponse<QuestionResponseData>>> => {
  return axios.get<DataResponse<QuestionResponseData>>(`${basePath}/${assessmentId}/questions`, {
    params,
  });
};

export const getParticipantsApi = async (
  basePath: string,
  assessmentId: number,
  params: DataParams,
): Promise<AxiosResponse<DataResponse<ParticipantData>>> => {
  return axios.get<DataResponse<ParticipantData>>(`${basePath}/${assessmentId}/participants`, {
    params,
  });
};

export const getParticipantDetailsApi = async (
  basePath: string,
  participantId: number,
  params?: Partial<DataParams>,
): Promise<AxiosResponse<DataResponse<ParticipantDetails>>> => {
  return axios.get<DataResponse<ParticipantDetails>>(`${basePath}/participant/${participantId}`, {
    params,
  });
};

export const getParticipantTextFieldGradingApi = async (
  basePath: string,
  participantId: number,
  params?: Partial<DataParams>,
): Promise<AxiosResponse<DataResponse<ParticipantDetails>>> => {
  return axios.get<DataResponse<ParticipantDetails>>(
    `${basePath}/participant/${participantId}/textfield-grading`,
    { params },
  );
};

export const saveCustomScoreApi = async (
  basePath: string,
  submissionId: number,
  data: { questionId: number; score: number },
): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  return axios.post<{ success: boolean; message: string }>(
    `${basePath}/participant/${submissionId}/textfield-score`,
    data,
  );
};
