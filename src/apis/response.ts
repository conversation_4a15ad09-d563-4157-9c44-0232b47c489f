import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { Response } from 'src/types/models';
import type { ChartData } from 'src/types/chart';

/**
 * Create response
 */
export async function createResponseApi(
  path: string,
  params: Response,
): Promise<AxiosResponse<Response>> {
  return api.post<Response>(path, params);
}

/**
 * Get all responses
 */
export async function getAllResponsesApi(path: string): Promise<AxiosResponse<Response[]>> {
  return api.get<Response[]>(path);
}

/**
 * Get response by ID
 */
export async function getResponseByIdApi(
  path: string,
  id: number,
): Promise<AxiosResponse<Response>> {
  return api.get<Response>(`${path}/${id}`);
}

/**
 * Find answer by submission and question ID
 */
export async function findAnswerApi(
  path: string,
  submissionId: number,
  questionId: number,
): Promise<AxiosResponse<Response>> {
  return api.get<Response>(`${path}/${submissionId}/${questionId}`);
}

/**
 * Find multiple answers by submission and question ID (checkbox)
 */
export async function findAnswersApi(
  path: string,
  submissionId: number,
  questionId: number,
): Promise<AxiosResponse<Response[]>> {
  return api.get<Response[]>(`${path}/checkbox/${submissionId}/${questionId}`);
}

/**
 * Find checkbox answer to remove
 */
export async function findRemoveCheckBoxAnswerApi(
  path: string,
  submissionId: number,
  questionId: number,
  selectedOptionId: number,
): Promise<AxiosResponse<Response>> {
  return api.get<Response>(`${path}/${submissionId}/${questionId}/${selectedOptionId}`);
}

/**
 * Update response
 */
export async function updateResponseApi(
  path: string,
  id: number,
  params: Response,
): Promise<AxiosResponse<Response>> {
  return api.patch<Response>(`${path}/${id}`, params);
}

/**
 * Delete response
 */
export async function deleteResponseApi(path: string, id: number): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/${id}`);
}

/**
 * Clear answer
 */
export async function clearAnswerApi(path: string, id: number): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/clear/${id}`);
}

/**
 * Get chart data
 */
export async function getChartDataApi(
  path: string,
  assessmentId: number,
): Promise<AxiosResponse<ChartData[]>> {
  return api.get<ChartData[]>(`${path}/chart-data/${assessmentId}`);
}

/**
 * Save user quiz response
 */
export async function saveUserQuizResponseApi(
  path: string,
  data: Response,
): Promise<AxiosResponse<Response>> {
  return api.post<Response>(`${path}/quiz/save-response`, data);
}

/**
 * Get number of responses
 */
export async function getNumberOfResponsesApi(
  assessmentId: number,
): Promise<AxiosResponse<{ number: number }>> {
  return api.get<{ number: number }>(`/assessments/responses/header/${assessmentId}`);
}

/**
 * Get response evaluation data by ID
 */
export async function getResponseEvaluationDataApi(
  id: number,
): Promise<AxiosResponse<{ data: ChartData[] }>> {
  return api.get(`assessments/dashboard/evaluate/${id}`);
}

/**
 * Get response header by ID
 */
export async function getResponseHeaderByIdApi(id: number): Promise<AxiosResponse<unknown>> {
  return api.get(`assessments/header/${id}`);
}

/**
 * Export assessment to Excel
 */
export async function exportAssessmentToExcelApi(id: number): Promise<AxiosResponse<Blob>> {
  return api.get(`assessments/${id}/export/excel`, {
    responseType: 'blob',
  });
}
