import type { QTableProps } from 'quasar';
import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { Role } from 'src/types/models';
import { formatParams } from 'src/utils/formatter';

// API Functions
const ROLES_PATH = 'roles';

/**
 * Create a new role
 */
export async function createRoleApi(dto: Role): Promise<AxiosResponse<Role>> {
  return api.post<Role>(ROLES_PATH, dto);
}

/**
 * Get role by ID
 */
export async function getRoleByIdApi(id: number): Promise<AxiosResponse<Role>> {
  return api.get<Role>(`${ROLES_PATH}/${id}`);
}

/**
 * Update role
 */
export async function updateRoleApi(id: number, data: Partial<Role>): Promise<AxiosResponse<Role>> {
  return api.patch<Role>(`${ROLES_PATH}/${id}`, data);
}

/**
 * Delete role
 */
export async function deleteRoleApi(id: number): Promise<AxiosResponse<Role>> {
  return api.delete<Role>(`${ROLES_PATH}/${id}`);
}

/**
 * Get all roles with pagination and search
 */
export async function getRolesApi(
  pagination: QTableProps['pagination'],
  search?: string,
): Promise<AxiosResponse<DataResponse<Role>>> {
  const params = formatParams(pagination, search);
  return api.get<DataResponse<Role>>(ROLES_PATH, { params });
}
