import { api as http } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { QTableProps } from 'quasar';
import type { Skill } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import type { CreateSkillRequest, UpdateSkillRequest } from 'src/types/api';
import { formatParams } from 'src/utils/formatter';

/**
 * Pure API function for getting all skills with pagination
 * @param pag - Pagination parameters
 * @param search - Search keyword
 * @param career_type - Career type filter
 * @returns Promise<AxiosResponse<DataResponse<Skill>>>
 */
export async function getAllSkillsApi(
  pag?: QTableProps['pagination'],
  search?: string | undefined,
  career_type?: Skill['career_type'],
): Promise<AxiosResponse<DataResponse<Skill>>> {
  const formattedParams = formatParams(pag, search);
  const params = {
    ...formattedParams,
    career_type: career_type,
  };

  return await http.get<DataResponse<Skill>>('/skills', { params });
}

/**
 * Pure API function for getting a single skill
 * @param id - Skill ID
 * @returns Promise<AxiosResponse<Skill>>
 */
export async function getOneSkillApi(id: number): Promise<AxiosResponse<Skill>> {
  return await http.get<Skill>(`/skills/${id}`);
}

/**
 * Pure API function for creating a skill
 * @param data - Skill data
 * @returns Promise<AxiosResponse<Skill>>
 */
export async function createSkillApi(data: CreateSkillRequest): Promise<AxiosResponse<Skill>> {
  return await http.post<Skill>('/skills', data);
}

/**
 * Pure API function for updating a skill
 * @param id - Skill ID
 * @param data - Updated skill data
 * @returns Promise<AxiosResponse<Skill>>
 */
export async function updateSkillApi(
  id: number,
  data: UpdateSkillRequest,
): Promise<AxiosResponse<Skill>> {
  return await http.patch<Skill>(`/skills/${id}`, data);
}

/**
 * Pure API function for deleting a skill
 * @param id - Skill ID
 * @returns Promise<AxiosResponse<void>>
 */
export async function deleteSkillApi(id: number): Promise<AxiosResponse<void>> {
  return await http.delete<void>(`/skills/${id}`);
}
