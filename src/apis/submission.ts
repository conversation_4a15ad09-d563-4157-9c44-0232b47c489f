import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { Submission } from 'src/types/models';

/**
 * Get submission by assessment ID
 */
export async function getSubmissionByAssessmentIdApi(
  path: string,
  assessmentId: number,
): Promise<AxiosResponse<Submission>> {
  return api.get<Submission>(`${path}/${assessmentId}`);
}

/**
 * Get draft submission
 */
export async function getDraftSubmissionApi(
  path: string,
  assessmentId: number,
  userId: number,
): Promise<AxiosResponse<Submission>> {
  return api.get<Submission>(`${path}/${assessmentId}/${userId}`);
}

/**
 * Create new submission
 */
export async function createSubmissionApi(
  path: string,
  data: Omit<Submission, 'id'>,
): Promise<AxiosResponse<Submission>> {
  return api.post<Submission>(path, data);
}

/**
 * Update submission
 */
export async function updateSubmissionApi(
  path: string,
  id: number,
  data: Partial<Submission>,
): Promise<AxiosResponse<Submission>> {
  return api.patch<Submission>(`${path}/${id}`, data);
}

/**
 * Get all submissions
 */
export async function getAllSubmissionsApi(path: string): Promise<AxiosResponse<Submission[]>> {
  return api.get<Submission[]>(path);
}

/**
 * Get single submission by ID
 */
export async function getSubmissionApi(
  path: string,
  id: number,
): Promise<AxiosResponse<Submission>> {
  return api.get<Submission>(`${path}/${id}`);
}

/**
 * Submit final submission
 */
export async function submitSubmissionApi(
  path: string,
  id: number,
): Promise<AxiosResponse<Submission>> {
  return api.patch<Submission>(`${path}/submit-assessment/${id}`);
}

/**
 * Start submission for assessment
 */
export async function startSubmissionApi(
  path: string,
  linkUrl: string,
  userId: number,
): Promise<AxiosResponse<Submission>> {
  return api.post<Submission>(`${path}/start-assessment`, {
    linkUrl,
    userId,
  });
}

/**
 * Delete submission
 */
export async function deleteSubmissionApi(path: string, id: number): Promise<AxiosResponse<void>> {
  return api.delete(`${path}/${id}`);
}
