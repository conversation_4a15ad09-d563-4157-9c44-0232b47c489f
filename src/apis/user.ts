import type { QTableProps } from 'quasar';
import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { DataResponse, FacultyWithUserStatus } from 'src/types/data';
import type { User } from 'src/types/models';
import { formatParams } from 'src/utils/formatter';

// API Functions
const USERS_PATH = 'users';

/**
 * Create a new user
 */
export async function createUserApi(dto: User): Promise<AxiosResponse<User>> {
  const { roleId, roles, ...restData } = dto;
  return api.post<User>(USERS_PATH, {
    ...restData,
    roles: roles,
    roleIds: roleId, // ส่ง roleId array
  });
}

/**
 * Get user by ID
 */
export async function getUserByIdApi(id: number): Promise<AxiosResponse<User>> {
  return api.get<User>(`${USERS_PATH}/${id}`);
}

/**
 * Update user
 */
export async function updateUserApi(id: number, data: Partial<User>): Promise<AxiosResponse<User>> {
  const { roleId, roles, ...restData } = data;
  return api.patch<User>(`${USERS_PATH}/${id}`, {
    ...restData,
    roles: roles,
    roleIds: roleId, // ส่ง roleId array
  });
}

/**
 * Delete user
 */
export async function deleteUserApi(id: number): Promise<AxiosResponse<User>> {
  return api.delete<User>(`${USERS_PATH}/${id}`);
}

/**
 * Get all users with pagination and search
 */
export async function getUsersApi(
  pagination?: QTableProps['pagination'],
  search?: string,
): Promise<AxiosResponse<DataResponse<User>>> {
  const params = formatParams(pagination, search);
  return api.get<DataResponse<User>>(USERS_PATH, { params });
}

/**
 * Get faculties with user status
 */
export async function getFacultiesWithUserStatusApi(
  userId: number,
  pagination: QTableProps['pagination'],
  search?: string,
): Promise<AxiosResponse<DataResponse<FacultyWithUserStatus>>> {
  const params = formatParams(pagination, search);
  return api.get<DataResponse<FacultyWithUserStatus>>(`${USERS_PATH}/${userId}/faculties`, {
    params,
  });
}

/**
 * Update user faculties
 */
export async function updateUserFacultiesApi(
  userId: number,
  facultyIds: number[],
): Promise<AxiosResponse<unknown>> {
  return api.patch(`${USERS_PATH}/${userId}/faculties`, {
    facultyIds,
  });
}

/**
 * * Update user roles
 */

export async function updateUserFacultiesRoleApi(
  userId: number,
  roleIds: number[],
): Promise<AxiosResponse<unknown>> {
  return api.patch(`${USERS_PATH}/${userId}/roles`, {
    roleIds,
  });
}
