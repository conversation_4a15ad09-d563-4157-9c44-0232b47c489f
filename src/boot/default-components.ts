import { QInput, QPagination, QTable } from 'quasar';
import type { ComponentConstructor, QInputProps, QTableProps } from 'quasar';
import { boot } from 'quasar/wrappers';

/**
 * Boot file to set default properties for Quasar components
 * This ensures consistent styling across the application
 * All input-related components will use 'accent' color by default
 */
export default boot(() => {
  // Set accent color as default for all input components
  SetComponentDefaults<QInputProps>(QInput, {
    color: 'accent',
  });
  // Table defaults
  SetComponentDefaults<QTableProps>(QTable, {
    bordered: true,
    flat: true,
    rowsPerPageOptions: [10, 25, 50, 100],
  });

  // Pagination defaults
  SetComponentDefaults<QTableProps['pagination']>(QPagination, {
    sortBy: 'id',
    page: 1,
    rowsPerPage: 10,
    descending: false, //ascending
    rowsNumber: 0,
  });
});

/**
 * Set some default properties on a component
 */

const SetComponentDefaults = <T>(
  component: ComponentConstructor<T>,
  defaults: Partial<T>,
): void => {
  (Object.keys(defaults) as (keyof typeof defaults)[]).forEach((prop: keyof typeof defaults) => {
    component.props[prop] =
      Array.isArray(component.props[prop]) === true || typeof component.props[prop] === 'function'
        ? {
            type: component.props[prop],
            default: defaults[prop],
          }
        : {
            ...component.props[prop],
            default: defaults[prop],
          };
  });
};
