<template>
  <div class="calendar-border">
    <div class="calendar-container">
      <!-- Calendar Header -->
      <div class="calendar-header q-pa-md row items-center" style="background: #fff">
        <div class="calendar-title text-h6 text-weight-bold row items-center">
          {{ monthOptions[selectedMonth]!.label }}
          <q-btn-dropdown flat dense class="q-ml-sm" :label="selectedYear" style="min-width: 80px">
            <q-list style="min-width: 100px">
              <q-item v-for="year in yearOptions" :key="year" clickable v-close-popup @click="setYear(year)">
                <q-item-section>{{ year }}</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>

        <div class="q-gutter-x-sm q-ml-auto">
          <q-btn flat round icon="chevron_left" @click="prevMonth" />
          <q-btn flat round icon="chevron_right" @click="nextMonth" />
        </div>
      </div>

      <!-- Calendar Body -->
      <q-calendar-month v-model:model-value="selectedDate" :day-min-height="90"
        :weekday-labels="['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส']" locale="th-TH" animated class="calendar-main">
        <template #day="{ scope }">
          <div class="q-pa-xs column items-center justify-center full-height day-container"
            :data-date="scope.timestamp.date">
            <!-- Day Number -->
            <div class="text-subtitle2">{{ scope.day }}</div>

            <!-- Single-Day Events Chip -->
            <template v-if="getSingleDayEventsOnDate(scope.timestamp.date).length">
              <div v-if="getSingleDayEventsOnDate(scope.timestamp.date).length" class="custom-chip"
                @click.stop="handleDayClick(scope.timestamp.date)">
                • {{ getSingleDayEventsOnDate(scope.timestamp.date).length }} กิจกรรม
              </div>
            </template>

            <!-- Multi-Day Bars, split per-week -->
            <template v-for="(chunk, idx) in getMultiDayChunksOn(scope.timestamp.date)"
              :key="chunk.id + '-' + chunk.start">
              <div class="multi-day-bar" :style="`
                  position: absolute;
                  top: ${getSingleDayEventsOnDate(scope.timestamp.date).length ? 40 : 15 + idx * 20}px;
                  left: 2px;
                  width: ${calculateEventWidth(chunk)}px;
                  height: 18px;
                  background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
                  border: 1px solid rgba(255,152,0,0.3);
                  border-radius: 9px;
                  display: flex;
                  align-items: center;
                  padding: 0 6px;
                  font-size: 10px;
                  font-weight: 500;
                  color: #333;
                  z-index: 10;
                  cursor: pointer;
                  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                `" @click.stop="handleDayClick(scope.timestamp.date)">
                {{ chunk.name }} ({{ getEventDuration(chunk) }} วัน)
              </div>
            </template>
          </div>
        </template>
      </q-calendar-month>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { CalendarEvent } from 'src/types/calendar';

// Convert Buddhist year → Gregorian
const buddhistToGregorian = (y: number) => y - 543;

// Today’s year/month in Buddhist calendar
const now = new Date();
const initYear = now.getFullYear() + 543;
const initMonth = now.getMonth();

// Reactive selected year, month, and date string
const selectedYear = ref<number>(initYear);
const selectedMonth = ref<number>(initMonth);
const selectedDate = ref<string>(
  `${buddhistToGregorian(initYear)}-${String(initMonth + 1).padStart(2, '0')}-01`
);

// Update selectedDate when year/month change
watch([selectedYear, selectedMonth], () => {
  selectedDate.value =
    `${buddhistToGregorian(selectedYear.value)}-${String(selectedMonth.value + 1).padStart(2, '0')}-01`;
});

// Prev / Next month handlers
function prevMonth() {
  if (selectedMonth.value === 0) {
    selectedMonth.value = 11;
    selectedYear.value--;
  } else {
    selectedMonth.value--;
  }
}
function nextMonth() {
  if (selectedMonth.value === 11) {
    selectedMonth.value = 0;
    selectedYear.value++;
  } else {
    selectedMonth.value++;
  }
}

// Year dropdown options
const yearOptions = Array.from({ length: 11 }, (_, i) => initYear - 5 + i);
function setYear(y: number) {
  selectedYear.value = y;
}

// Month labels
const monthOptions: { label: string; value: number }[] = [
  { label: 'มกราคม', value: 0 },
  { label: 'กุมภาพันธ์', value: 1 },
  { label: 'มีนาคม', value: 2 },
  { label: 'เมษายน', value: 3 },
  { label: 'พฤษภาคม', value: 4 },
  { label: 'มิถุนายน', value: 5 },
  { label: 'กรกฎาคม', value: 6 },
  { label: 'สิงหาคม', value: 7 },
  { label: 'กันยายน', value: 8 },
  { label: 'ตุลาคม', value: 9 },
  { label: 'พฤศจิกายน', value: 10 },
  { label: 'ธันวาคม', value: 11 }
];

// Sample events array (replace with your props/API)
const todayStr = new Date().toISOString().split('T')[0]!;
const tomorrow = new Date();
tomorrow.setDate(new Date().getDate() + 1);
const tomorrowStr = tomorrow.toISOString().split('T')[0]!;

interface EventChunk { start: string; end: string; id: string | number; name: string; }

const events: CalendarEvent[] = [
  { id: 1, name: 'โครงการสานสัมพันธ์บุคลากรใหม่', start: todayStr, end: todayStr },
  { id: 2, name: 'ประชุมทีม', start: todayStr, end: todayStr },
  { id: 3, name: 'อบรมความปลอดภัย', start: tomorrowStr, end: tomorrowStr },
  { id: 4, name: 'กิจกรรมกีฬา', start: '2025-06-12', end: '2025-06-12' },
  { id: 5, name: 'สัมมนาวิชาการ', start: '2025-06-13', end: '2025-06-15' },
  { id: 6, name: 'กิจกรรมอาสา', start: '2025-06-13', end: '2025-06-13' },
  { id: 7, name: 'งานเลี้ยงบริษัท', start: '2025-06-14', end: '2025-06-16' },
  { id: 8, name: 'ตรวจสุขภาพ', start: '2025-06-14', end: '2025-06-14' },
  { id: 9, name: 'กิจกรรม CSR', start: '2025-06-18', end: '2025-06-20' },
  { id: 10, name: 'โครงการอบรมภายใน', start: '2025-07-12', end: '2025-07-14' },
  { id: 11, name: 'กิจกรรมสัมพันธ์', start: '2025-07-12', end: '2025-07-12' },
  { id: 12, name: 'กิจกรรมพิเศษ', start: '2025-07-15', end: '2025-07-17' }
];

// 1) Single-day events filter
function getSingleDayEventsOnDate(date: string) {
  return events.filter(e => e.start === date && (!e.end || e.end === e.start));
}

// 2) Split multi-day event into week-bounded chunks
function getEventChunks(evt: CalendarEvent) {
  const res: { start: string; end: string }[] = [];
  let cursor = new Date(evt.start);
  const final = new Date(evt.end!);

  while (cursor <= final) {
    const endOfWeek = new Date(cursor);
    endOfWeek.setDate(cursor.getDate() + (6 - cursor.getDay()));
    const chunkEnd = endOfWeek < final ? endOfWeek : final;

    res.push({
      start: cursor.toISOString().split('T')[0]!,
      end: chunkEnd.toISOString().split('T')[0]!
    });

    cursor = new Date(chunkEnd);
    cursor.setDate(cursor.getDate() + 1);
  }
  return res;
}

// 3) Get chunks starting on date
function getMultiDayChunksOn(date: string): EventChunk[] {
  return events.flatMap(evt => {
    if (!evt.end || evt.start === evt.end) return [];
    return getEventChunks(evt).map(c => ({ id: evt.id, name: evt.name, ...c }));
  }).filter(c => c.start === date);
}

// 4) Duration inclusive
function getEventDuration(e: { start: string; end?: string }) {
  if (!e.end || e.end === e.start) return 1;
  const diff = new Date(e.end).getTime() - new Date(e.start).getTime();
  return Math.ceil(diff / (1000 * 60 * 60 * 24)) + 1;
}

// 5) Bar width calc
function calculateEventWidth(e: { start: string; end?: string }) {
  const s = new Date(e.start);
  const en = new Date(e.end || e.start);
  const days = Math.ceil((en.getTime() - s.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  const base = 126;
  return Math.max(days * base - 8, 120);
}

// Emit handler
const emit = defineEmits<{
  (e: 'day-clicked', evts: CalendarEvent[], date: string): void
}>();

function handleDayClick(date: string) {
  console.log('Day clicked:', date);
  const dayEvts = events.filter(ev => date >= ev.start && date <= (ev.end || ev.start));
  emit('day-clicked', dayEvts, formatThaiDate(date));
}


// Format date to Thai
function formatThaiDate(date: string) {
  const [y, m, d] = date.split('-');
  const mi = Number(m) - 1;
  const monthLabel = monthOptions[mi]?.label || '';
  return `${Number(d)} ${monthLabel} ${Number(y) + 543}`;
}

</script>

<style scoped>
.calendar-border {
  border: 2px solid #d9d9d9;
  border-radius: 12px;
  padding: 8px;
  background: #fff;
  width: fit-content;
}

.calendar-container {
  display: flex;
  flex-direction: column;
  width: 885px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  background: #fff;
  border-bottom: 1px solid #f5d76e;
  border-radius: 8px 8px 0 0;
  display: flex;
}

.calendar-main {
  flex: 1;
  background: #fff;
}

:deep(.q-calendar-month__head) {
  background: var(--q-primary);
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: black !important;
}

:deep(.q-calendar-month__head .q-calendar__head--weekday) {
  font-weight: 600;
  color: black !important;
  font-size: 16px;
  text-align: center;
}

:deep(.q-calendar-month__day) {
  border: 1px solid #e0e0e0;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 4px;
  overflow: visible !important;
}

.event-chip {
  pointer-events: auto !important;
  z-index: 10;
}

.event-chip:hover {
  background: #1976d2 !important;
  color: #fff !important;
}

.day-container {
  position: relative;
}

.multi-day-bar:hover {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.custom-chip {
  background: #f2c037;
  color: black;
  padding: 2px 10px;
  font-size: 10px;
  border-radius: 999px;
  text-align: center;
  width: 100%;
  max-width: 100%;
  cursor: pointer;
  z-index: 20;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: -20px;
}
</style>
