<template>
  <q-dialog v-model="dialogModel" persistent>
    <q-card class="q-pa-lg delete-dialog-card">
      <q-card-section class="delete-dialog-section">
        <div class="text-h6 text-weight-bold delete-dialog-title">{{ title }}</div>
        <q-icon name="delete" color="negative" size="40px" class="q-my-md delete-dialog-icon" />
        <div class="delete-dialog-message">{{ message }}</div>
      </q-card-section>
      <q-card-actions align="center" class="delete-dialog-actions">
        <q-btn
          flat
          color="grey-7"
          icon="close"
          label="ยกเลิก"
          class="delete-dialog-btn custom-cancel-btn"
          @click="onCancel"
        />
        <q-btn
          color="negative"
          unelevated
          icon="delete"
          label="ลบ"
          class="delete-dialog-btn custom-delete-btn"
          @click="onConfirm"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    default: 'ยืนยันการลบ',
  },
  message: {
    type: String,
    default: 'คุณต้องการลบข้อมูลนี้หรือไม่?',
  },
});
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const dialogModel = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

function onCancel() {
  emit('update:modelValue', false);
  emit('cancel');
}
function onConfirm() {
  emit('update:modelValue', false);
  emit('confirm');
}
</script>

<style scoped>
.delete-dialog-card {
  min-width: 360px;
  max-width: 95vw;
  border-radius: 24px;
  padding: 24px 24px 18px 24px !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.delete-dialog-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
  padding: 0;
}
.delete-dialog-title {
  width: 100%;
  text-align: center;
}
.delete-dialog-icon {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.delete-dialog-message {
  margin: 0 auto 16px auto;
  text-align: center;
  font-size: 1.08rem;
  color: #222;
  max-width: 90%;
  font-weight: 500;
}
.delete-dialog-actions {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
  margin-bottom: 2px;
}
.delete-dialog-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  gap: 8px;
  min-width: 140px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  box-sizing: border-box;
  padding: 0 18px;
  text-align: center;
}
.custom-cancel-btn {
  width: 140px;
  background-color: #f0f0f0 !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
}
.custom-cancel-btn:hover,
.custom-cancel-btn:focus {
  background-color: #e0e0e0 !important;
}
.custom-delete-btn {
  width: 140px;
  background-color: #ab2433 !important;
  color: white !important;
  border: none !important;
  transition: background-color 0.3s;
}
.custom-delete-btn:hover,
.custom-delete-btn:focus {
  background-color: #b71c1c !important;
}
</style>
