<template>
  <q-dialog
    ref="dialogRef"
    transition-duration="100"
    transition-hide="fade"
    transition-show="fade"
    :full-width="fullWidth"
    @hide="onDialogHide"
    :persistent
  >
    <q-card class="dialog-form" :style="{ width: width ? width : '500px', maxWidth: '100vw' }">
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pb-none">
          <div class="row">
            <div class="col text-h6 text-weight-medium">{{ title }}</div>

            <q-btn fab-mini flat padding="none" icon="close" @click="handleCloseDialog"></q-btn>
          </div>
          <div class="q-py-sm">
            <slot />
          </div>
        </q-card-section>
        <q-card-actions v-if="!hideActions" class="q-mx-sm q-mb-sm q-pt-none">
          <div v-if="title !== 'View'" class="full-width">
            <q-btn
              color="positive"
              unelevated
              class="full-width"
              :label="t(ctaText || 'save')"
              @click="submitForm"
              type="submit"
            />
          </div>
          <div v-else>
            <q-btn class="full-width" flat :label="t('ok')" type="submit"></q-btn>
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { QForm, useDialogPluginComponent } from 'quasar';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
// Props
defineProps<{
  title: string;
  width?: string;
  fullWidth?: boolean;
  ctaText?: string;
  hideActions?: true;
  persistent?: boolean;
}>();

const emits = defineEmits([...useDialogPluginComponent.emits, 'submit']);

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<InstanceType<typeof QForm>>();
const isFormValid = ref(false);

const validateForm = async () => {
  if (formRef.value) {
    isFormValid.value = await formRef.value.validate(false);
  }
};

defineExpose({
  dialogRef,
  isFormValid,
  formRef,
  validateForm,
});

defineModel<boolean>('formValid', { default: true });

const handleCloseDialog = () => {
  onDialogCancel();
};

const submitForm = async () => {
  await validateForm();
  if (isFormValid.value) {
    emits('submit');
    onDialogOK();
  }
};

// Watchers
watch(
  () => dialogRef.value,
  (newValue) => {
    if (!newValue) {
      isFormValid.value = false; // Reset validation when dialog closes
    }
  },
);
</script>
