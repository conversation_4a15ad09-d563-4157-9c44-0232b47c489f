<template>
  <div class="top-header-table">
    <!-- แถวหลัก แบ่งซ้าย-ขวา -->
    <div class="row q-mb-sm q-mt-sm justify-between">
      <!-- :white_check_mark: ฝั่งซ้าย: Title + Tab + Subtitle -->
      <div class="col-auto">
        <div class="text-h6">{{ (title || '') + (subtitle || '') }}</div>
        <slot name="tab" />
      </div>

      <!-- :white_check_mark: ฝั่งขวา: Search + ปุ่มเพิ่ม -->
      <div class="items-center q-gutter-sm">
        <div class="row items-center justify-between q-mt-xl flex" style="margin-top: 100px">
          <!-- ซ้าย: SearchBar -->
          <div class="col">
            <SearchBar v-if="showSearch" @search="handleSearch" />
          </div>
          <div class="col-auto">
            <q-btn
              v-if="showCreateButton"
              :label="createButtonLabel"
              color="accent"
              icon="add"
              @click="handleCreate"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <CreateSkillDialog
    v-if="skillStore.createDialog && pageType === 'skill'"
    v-model="skillStore.createDialog"
    :skill-type="skillStore.selectedTap"
    :title="skillStore.dialogTitile"
  />
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';
import { useSkillStore } from 'src/stores/skills';
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';
import { onMounted } from 'vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
  subtitle?: string;
  pageType?: 'skill' | 'competency';
}
const skillStore = useSkillStore();

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

const props = withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
});

onMounted(() => {
  console.log(props.title);
});
const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  if (props.pageType === 'skill') {
    skillStore.dialogTitile = `สร้างความรู้และทักษะ${skillStore.selectedTap}`;
    skillStore.createDialog = true;
  }
  emit('create');
};
</script>
