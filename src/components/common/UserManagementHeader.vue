<template>
  <div class="top-header-table">
    <div class="row q-mt-sm items-center">
      <div class="col-auto">
        <div class="text-h6">{{ title }}</div>
      </div>
    </div>
    <div class="row items-center">
      <div class="col-auto">
        <slot name="tab" />
      </div>
    </div>
    <div class="row q-mt-md q-mb-sm items-center justify-end q-pr-none">
      <div class="row items-center q-gutter-md q-pr-none">
        <!-- ตัวที่หนึ่ง -->
        <div v-if="showDepartmentFilter">
          <q-select
            v-model="selectedDepartment"
            outlined
            dense
            multiple
            style="width: 220px"
            label="หน่วยงาน/ส่วนงาน"
            :options="[
              { label: 'ส่วนงาน A', value: 'A' },
              { label: 'ส่วนงาน B', value: 'B' },
              { label: 'ส่วนงาน C', value: 'C' },
              { label: 'ส่วนงาน D', value: 'D' },
            ]"
            clearable
          />
        </div>
        <!-- ตัวที่สอง -->
        <div v-if="showRoleFilter">
          <q-select
            v-model="selectedRole"
            outlined
            dense
            multiple
            style="width: 220px"
            label="บทบาท"
            :options="[
              { label: 'บทบาท A', value: 'A' },
              { label: 'บทบาท B', value: 'B' },
              { label: 'บทบาท C', value: 'C' },
              { label: 'บทบาท D', value: 'D' },
            ]"
            clearable
          />
        </div>
        <div v-if="showSearch">
          <SearchBar @search="handleSearch" />
        </div>
        <div v-if="showCreateButton">
          <q-btn :label="createButtonLabel" color="accent" icon="add" @click="handleCreate" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';
import { ref, onMounted } from 'vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
  showDepartmentFilter?: boolean;
  showRoleFilter?: boolean;
  subtitle?: string;
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

const props = withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
  showDepartmentFilter: false,
  showRoleFilter: false,
});
const selectedDepartment = ref(null);
const selectedRole = ref(null);

onMounted(() => {
  console.log(props.title);
});
const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  emit('create');
};
</script>
