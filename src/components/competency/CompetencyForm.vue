<!-- <template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container">
      <q-card-section>
        <div class="text-h6">{{ title }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-input
              v-model="formDataRef.name"
              label="ชื่อสมรรถนะ"
              :rules="[(val) => !!val || 'กรุณากรอกชื่อสมรรถนะ']"
              outlined
            />
            <q-select
              v-model="formDataRef.type"
              outlined
              label="ประเภทของสมรรถนะ"
              :options="[]"
            ></q-select>
            <q-select
              v-model="formDataRef.career"
              outlined
              label="ประเภทของสายงาน"
              :options="[]"
            ></q-select>
            <q-input
              v-model="formDataRef.description"
              label="คำอธิบายเพิ่มเติม"
              type="textarea"
              outlined
            />
          </div>
        </q-card-section>
        <q-card-actions align="center" class="row">
          <q-btn label="ยกเลิก" class="col" flat color="grey-7" @click="onDialogCancel" />
          <q-btn label="บันทึก" class="col" color="accent" @click="submitForm" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Competency } from 'src/types/models';

const props = defineProps<{
  title: string;
  formData?: Competency;
}>();

// Properly destructure the dialog plugin component
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<QForm | null>(null);
const formDataRef = ref<Competency>({
  id: 0,
  name: '',
  description: '',
  type: '',
  career: '',
});

onMounted(() => {
  if (props.formData) {
    formDataRef.value = { ...props.formData };
  } else {
    formDataRef.value = {
      id: 0,
      name: '',
      description: '',
      type: '',
      career: '',
    };
  }
});

const submitForm = async () => {
  if (formRef.value) {
    try {
      const isValid = await formRef.value.validate();
      if (isValid) {
        onDialogOK(formDataRef.value);
      }
    } catch (error) {
      console.error('Form validation error:', error);
    }
  }
};
</script>

<style scoped></style> -->

<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container" style="min-width: 640px; max-width: 800px">
      <q-card-section>
        <div class="text-h6">{{ title }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <div class="q-pa-md" style="border: 1px solid #ccc; border-radius: 10px">
              <p class="text-subtitle1">ชื่อสมรรถนะ</p>
              <q-input
                v-model="formDataRef.name"
                label="ชื่อสมรรถนะ"
                :rules="[(val) => !!val || 'กรุณากรอกชื่อสมรรถนะ']"
                outlined
                class="q-mt-sm"
                :readonly="readOnly"
              />
            </div>
            <div class="q-pa-md" style="border: 1px solid #ccc; border-radius: 10px">
              <p class="text-subtitle1">ประเภทสมรรถนะ</p>
              <q-select
                v-model="formDataRef.career_type"
                outlined
                label="ประเภทสมรรถนะ"
                :options="careerTypeOptions"
                :rules="[(val) => !!val || 'กรุณาเลือกประเภทสมรรถนะ']"
                :readonly="readOnly"
              />
            </div>
            <div class="q-pa-md" style="border: 1px solid #ccc; border-radius: 10px">
              <p class="text-subtitle1">คำอธิบายเพิ่มเติม</p>
              <q-input
                v-model="formDataRef.description"
                label="คำอธิบายเพิ่มเติม"
                type="textarea"
                outlined
                :readonly="readOnly"
              />
            </div>
          </div>
        </q-card-section>
        <q-card-actions class="q-card__section justify-end q-gutter-sm" style="padding-right: 16px">
          <q-btn
            label="ยกเลิก"
            flat
            color="grey-7"
            icon="close"
            class="q-px-md q-py-sm items-center"
            style="font-size: 15px; gap: 4px"
            @click="onDialogCancel"
          />
          <q-btn
            v-if="!readOnly"
            label="ยืนยัน"
            color="positive"
            icon="check"
            unelevated
            class="q-px-md q-py-sm items-center"
            style="font-size: 15px; gap: 4px"
            @click="submitForm"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Competency } from 'src/types/models';
import { useCompetencyStore } from 'src/stores/competencies';

const store = useCompetencyStore();

const careerTypeOptions = [
  { label: 'สมรรถนะหลัก', value: 'สมรรถนะหลัก' },
  { label: 'สมรรถนะสายวิชาการ', value: 'สมรรถนะสายวิชาการ' },
  { label: 'สมรรถนะสายสนับสนุนวิชาการ', value: 'สมรรถนะสายสนับสนุนวิชาการ' },
  { label: 'สมรรถนะทางการบริหาร', value: 'สมรรถนะทางการบริหาร' },
];

const props = defineProps<{
  title: string;
  formData?: Competency;
  readOnly?: boolean;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<QForm | null>(null);
const formDataRef = ref<Competency>({
  id: 0,
  name: '',
  description: '',
  career_type: 'วิชาการ',
});

onMounted(() => {
  if (props.formData) {
    formDataRef.value = { ...props.formData };
  } else {
    formDataRef.value = {
      id: 0,
      name: '',
      description: '',
      career_type: 'วิชาการ',
    };
  }
});

function normalizeFormData(formData: Competency) {
  const data = { ...formData };
  if (!props.formData?.id) {
    delete data.id;
  }

  return data;
}

async function handleCompetency(formData: Competency) {
  if (props.formData?.id) {
    await store.updateCompetency(props.formData.id, formData);
  } else {
    await store.addCompetency(formData);
  }
}

const submitForm = async () => {
  if (!formRef.value) return;
  try {
    const isValid = await formRef.value.validate();
    if (!isValid) return;
    const formData = normalizeFormData(formDataRef.value);
    await handleCompetency(formData);
    onDialogOK(formData);
  } catch (error) {
    console.error('Form validation error:', error);
  }
};
</script>
