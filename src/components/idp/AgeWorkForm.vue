<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container">
      <q-card-section>
        <div class="text-h6">{{ dialogTitle }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-input
              v-model="formDataRef.name"
              label="ชื่อช่วงอายุการปฏิบัติงาน"
              :rules="[(val) => !!val || 'กรุณากรอกชื่อช่วงอายุการปฏิบัติงาน']"
              outlined
            />

            <div class="row q-gutter-x-md">
              <div class="col">
                <q-input
                  v-model.number="formDataRef.startYear"
                  label="ปีเริ่มต้น"
                  type="number"
                  :rules="[
                    (val) =>
                      (val !== null && val !== undefined && val >= 0) || 'กรุณากรอกปีเริ่มต้น',
                    (val) => val < formDataRef.endYear || 'ปีเริ่มต้นต้องน้อยกว่าปีสิ้นสุด',
                  ]"
                  outlined
                />
              </div>
              <div class="col">
                <q-input
                  v-model.number="formDataRef.endYear"
                  label="ปีสิ้นสุด"
                  type="number"
                  :rules="[
                    (val) =>
                      (val !== null && val !== undefined && val >= 0) || 'กรุณากรอกปีสิ้นสุด',
                    (val) => val > formDataRef.startYear || 'ปีสิ้นสุดต้องมากกว่าปีเริ่มต้น',
                  ]"
                  outlined
                />
              </div>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="ยกเลิก" @click="onDialogCancel" />
          <q-btn type="submit" color="primary" label="บันทึก" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent } from 'quasar';
import type { AgeWork } from 'src/types/idp';
import { ref, reactive, onMounted, computed } from 'vue';

defineEmits([...useDialogPluginComponent.emits]);

const props = defineProps<{
  title?: string;
  formData?: AgeWork;
  criteriaId: number;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const dialogTitle = computed(() => {
  return props.formData ? 'แก้ไขช่วงอายุการปฏิบัติงาน' : 'สร้างช่วงอายุการปฏิบัติงานใหม่';
});

const formRef = ref();
const formDataRef = reactive<Omit<AgeWork, 'id'>>({
  name: '',
  startYear: 0,
  endYear: 0,
  ageWorkCriteriaId: props.criteriaId,
});

onMounted(() => {
  if (props.formData) {
    formDataRef.name = props.formData.name;
    formDataRef.startYear = props.formData.startYear;
    formDataRef.endYear = props.formData.endYear;
    formDataRef.ageWorkCriteriaId = props.formData.ageWorkCriteriaId;
  }
});

const submitForm = async () => {
  try {
    const isValid = await formRef.value.validate();
    if (isValid) {
      onDialogOK(formDataRef);
    }
  } catch (error: unknown) {
    console.error('Form validation error:', error);
  }
};
</script>

<style scoped>
.container {
  min-width: 500px;
}
</style>
