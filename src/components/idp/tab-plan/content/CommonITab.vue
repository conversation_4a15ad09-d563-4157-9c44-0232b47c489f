<template>
  <div>
    <!-- Age Work Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">อายุการปฏิบัติงาน</div>
      <div class="q-mb-sm text-caption">{{ formData.ageWork }}</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="ageOption in ageWorkOptions"
          :key="ageOption.value"
          v-model="formData.ageWork"
          :val="ageOption.value"
          :label="ageOption.label"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Knowledge and Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ความรู้และทักษะ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะใหม่" @click="onAddSkill" />
      </div>

      <!-- Search Filters -->
      <div class="row q-gutter-sm q-mb-md">
        <div class="col-auto flex items-center">
          <span class="text-subtitle2">ค้นหาโดย:</span>
        </div>
        <div class="col-4">
          <q-select
            v-model="filters.skillType"
            label="ความรู้และทักษะทั่วไปของบุคลากร"
            :options="skillTypeOptions"
            outlined
            dense
            color="accent"
          />
        </div>
        <div class="col-4">
          <q-select
            v-model="filters.status"
            label="ประเภทสถานะงาน"
            :options="statusOptions"
            outlined
            color="accent"
            dense
          />
        </div>
      </div>

      <!-- Skills Search -->
      <div class="q-mb-md">
        <q-select
          v-model="selectedSkill"
          label="ค้นหาทักษะ"
          :options="searchableSkills"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterSkills"
          option-value="id"
          option-label="name"
          style="width: 100%"
          @update:model-value="addSelectedSkill"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <div v-if="formData.skills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะที่เลือก:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #f5f5f5;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>

      <!-- Skills List Display -->
      <div v-if="displaySkills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">รายการทักษะ:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in displaySkills"
            :key="skill.id || 0"
            class="col-12 skill-display-item q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div>
              <div class="text-weight-medium">{{ skill.name }}</div>
              <div class="text-caption text-grey-6">{{ skill.description }}</div>
            </div>
            <q-btn
              icon="delete"
              size="sm"
              flat
              round
              color="negative"
              @click="removeDisplaySkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData, AgeWorkOption, SkillTypeOption } from 'src/types/idp-tab-plan';
import { QSelect } from 'quasar';

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
  }),
});

const filters = ref({
  skillType: null,
  status: null,
});

const skillTypeOptions = ref<SkillTypeOption[]>([
  { label: 'ทักษะทั่วไป', value: 'general' },
  { label: 'ทักษะเฉพาะทาง', value: 'specialized' },
]);

const statusOptions = ref<SkillTypeOption[]>([
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
]);

const ageWorkOptions = ref<AgeWorkOption[]>([
  { label: 'อายุงาน 1 - 2 ปี', value: '1-2YEAR' },
  { label: 'อายุงาน 3 - 5 ปี', value: '3-5YEAR' },
  { label: 'อายุงาน 6 - 8 ปี', value: '6-8YEAR' },
  { label: 'อายุงานตั้งแต่ 9 ปีขึ้นไป', value: '9UP' },
]);

const selectedSkill = ref<Skill | null>(null);

// Mock display skills (from the image)
const displaySkills = ref<Skill[]>([
  {
    id: 1,
    name: 'การเขียนหนังสือออกอนุอาสนรรคชาต',
    description: 'ทักษะการเขียนเอกสาร',
    career_type: 'core',
    dep_id: 1,
    evaluatorId: 1,
    tracking: true,
    programId: 1,
    competencyIds: [1],
  },
  {
    id: 2,
    name: 'การพัฒนาคุณภาพการให้บริการ',
    description: 'ทักษะการบริการ',
    career_type: 'support',
    dep_id: 1,
    evaluatorId: 1,
    tracking: true,
    programId: 1,
    competencyIds: [2],
  },
]);

const searchableSkills = ref<Skill[]>([]);

// Methods
const onAddSkill = () => {
  console.log('Add new skill');
  // Implement add skill functionality - could open another dialog
};

const removeDisplaySkill = (skillId: number) => {
  displaySkills.value = displaySkills.value.filter((skill) => skill.id !== skillId);
};

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};

const addSelectedSkill = (skill: Skill) => {
  if (skill && !formData.value.skills.some((s) => s.id === skill.id)) {
    formData.value.skills.push(skill);
    selectedSkill.value = null; // Reset selected skill after adding
  }
};

const filterSkills: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  if (inputValue === '') {
    doneFn(() => {
      searchableSkills.value = displaySkills.value;
    });
    return;
  }

  const filtered = displaySkills.value.filter((skill: Skill) =>
    skill.name.toLowerCase().includes(inputValue.toLowerCase()),
  );

  doneFn(() => {
    searchableSkills.value = filtered;
  });
};

// Initialize searchableSkills
onMounted(() => {
  searchableSkills.value = displaySkills.value;
});
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
