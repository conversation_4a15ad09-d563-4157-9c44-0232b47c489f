<template>
  <div>
    <!-- Age Work Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">อายุการปฏิบัติงาน (ข้าราชการระดับกลาง)</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="ageOption in ageWorkOptions"
          :key="ageOption.value"
          v-model="formData.ageWork"
          :val="ageOption.value"
          :label="ageOption.label"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Management Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ทักษะการบริหารจัดการ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะการบริหาร" @click="onAddSkill" />
      </div>

      <!-- Management specific content -->
      <div class="q-mb-md">
        <q-select
          v-model="selectedSkill"
          label="ค้นหาทักษะการบริหารจัดการ"
          :options="managementSkills"
          outlined
          dense
          use-input
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="groups" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <div v-if="formData.skills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะการบริหารที่เลือก:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #f0f8ff;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData, AgeWorkOption } from 'src/types/idp-tab-plan';

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
  }),
});

const ageWorkOptions = ref<AgeWorkOption[]>([
  { label: 'อายุงาน 5 - 10 ปี', value: '3-5YEAR' },
  { label: 'อายุงาน 10 - 15 ปี', value: '6-8YEAR' },
  { label: 'อายุงานตั้งแต่ 15 ปีขึ้นไป', value: '9UP' },
]);

const selectedSkill = ref<Skill | null>(null);

const managementSkills = ref<Skill[]>([
  {
    id: 10,
    name: 'การบริหารทีม',
    description: 'ทักษะการนำทีม',
    career_type: 'core',
    dep_id: 1,
    evaluatorId: 1,
    tracking: true,
    programId: 1,
    competencyIds: [1],
  },
  {
    id: 11,
    name: 'การวางแผนเชิงกลยุทธ์',
    description: 'ทักษะการวางแผนองค์กร',
    career_type: 'core',
    dep_id: 1,
    evaluatorId: 1,
    tracking: true,
    programId: 1,
    competencyIds: [2],
  },
]);

const onAddSkill = () => {
  console.log('Add management skill');
};

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
