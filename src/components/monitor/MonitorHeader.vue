<template>
  <div class="top-header-table">
    <!-- แถวหลัก แบ่งซ้าย-ขวา -->
    <div class="row q-mb-sm q-mt-sm justify-between">
      <!-- :white_check_mark: ฝั่งซ้าย: Title + Tab + Subtitle -->
      <div class="col-auto">
        <div class="text-h6">{{ (title || '') + (subtitle || '') }}</div>
        <slot name="tab" />
      </div>

      <!-- :white_check_mark: ฝั่งขวา: Search + ปุ่มเพิ่ม -->
      <div class="items-center q-gutter-sm">
        <!-- Filter Row -->
        <div class="row items-center justify-between q-mt-xl flex" style="margin-top: 100px">
          <!-- ซ้าย: SearchBar -->
          <div class="row q-gutter-md q-mb-md" v-if="showFilter">
            <div>
              <q-select
                v-model="selectedFaculty"
                :options="facultyOptions"
                label="คณะ/ส่วนงาน"
                outlined
                dense
                clearable
                style="width: 300px"
                @update:model-value="handleFacultyChange"
              />
            </div>
            <div>
              <q-select
                v-model="selectedPosition"
                :options="positionOptions"
                label="ตำแหน่ง"
                outlined
                dense
                clearable
                style="width: 220px"
                @update:model-value="handlePositionChange"
              />
            </div>
            <div>
              <q-select
                v-model="selectedWorkAge"
                :options="workAgeOptions"
                label="ช่วงอายุงาน"
                outlined
                dense
                clearable
                style="width: 220px"
                @update:model-value="handleWorkAgeChange"
              />
            </div>
            <div>
              <SearchBar @search="handleSearch" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';
import { onMounted, ref } from 'vue';

interface Props {
  title: string;
  showCreateButton?: boolean;
  showFilter?: boolean;
  subtitle?: string;
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'facultyChange', value: string): void;
  (e: 'positionChange', value: string): void;
  (e: 'workAgeChange', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showFilter: true,
});

const selectedFaculty = ref('');
const selectedPosition = ref('');
const selectedWorkAge = ref('');

const facultyOptions = ref([{ label: 'คณะวิทยาการสารสนเทศ', value: 'informatics' }]);

const positionOptions = ref([{ label: 'อาจารย์', value: 'lecturer' }]);

const workAgeOptions = ref([{ label: '1-5 ปี', value: '1-5' }]);

onMounted(() => {
  console.log(props.title);
});

const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleFacultyChange = (value: string) => {
  emit('facultyChange', value);
};

const handlePositionChange = (value: string) => {
  emit('positionChange', value);
};

const handleWorkAgeChange = (value: string) => {
  emit('workAgeChange', value);
};
</script>
