<template>
  <div style="margin-top: 32px">
    <q-card
      class="q-pa-lg q-mx-auto"
      style="
        width: 620px;
        min-width: 520px;
        height: 300px;
        border: 1px solid #dddddd;
        border-radius: 20px;
      "
    >
      <div class="text-lg font-semibold mb-2">
        {{ skill?.name || '—' }}
      </div>
      <div class="mb-4">
        <div>คำอธิบายทักษะ...............................................................</div>
        <div>
          ....................................................................................................................
        </div>
        <div>
          ....................................................................................................................
        </div>
      </div>
      <div class="font-semibold underline mb-2">สมรรถนะที่เชื่อมโยงกับทักษะ</div>
      <div class="flex gap-2 mb-4">
        <q-btn
          label="การทำงานเป็นทีม"
          :color="selected === 'team' ? 'primary' : 'white'"
          :text-color="selected === 'team' ? 'black' : 'grey-7'"
          rounded
          @click="selected = 'team'"
          class="q-px-md"
        />
        <q-btn
          label="ความคิดสร้างสรรค์และนวัตกรรม"
          :color="selected === 'creativity' ? 'primary' : 'white'"
          :text-color="selected === 'creativity' ? 'black' : 'grey-7'"
          rounded
          @click="selected = 'creativity'"
          class="q-px-md"
        />
        <q-btn
          label="การพัฒนาอย่างยั่งยืน"
          :color="selected === 'sustainable' ? 'primary' : 'white'"
          :text-color="selected === 'sustainable' ? 'black' : 'grey-10'"
          rounded
          @click="selected = 'sustainable'"
          class="q-px-md q-font-bold"
        />
      </div>
      <q-separator spaced />
      <div class="font-semibold mb-2">{{ competencyMap[selected].name }}</div>
      <div>คำอธิบายสมรรถนะ................</div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

type CompetencyKey = 'team' | 'creativity' | 'sustainable';
const selected = ref<CompetencyKey>('sustainable');
defineProps<{ skill?: { id: number; name: string } }>();

const competencyMap: Record<CompetencyKey, { name: string }> = {
  team: {
    name: 'การทำงานเป็นทีม',
  },
  creativity: {
    name: 'ความคิดสร้างสรรค์และนวัตกรรม',
  },
  sustainable: {
    name: 'การพัฒนาอย่างยั่งยืน',
  },
};
</script>
