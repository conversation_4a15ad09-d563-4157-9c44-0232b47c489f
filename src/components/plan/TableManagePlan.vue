<template>
  <div style="margin-top: 32px">
    <q-table
      class="custom-table"
      :rows="localRows"
      :columns="columns"
      row-key="id"
      flat
      style="width: 200px; font-size: 12px"
    >
      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th v-for="col in props.cols" :key="col.name" :props="props" style="text-align: center">
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props" @click="selectRow(props.row.id)" class="row-hover">
          <q-td v-for="col in props.cols" :key="col.name" :props="props" class="cell-relative">
            <template v-if="col.name === 'name'">
              <template v-if="props.row.hasSkill">
                <q-icon name="check" color="positive" size="24px" /> &nbsp;{{ props.row.name }}
              </template>
              <template v-else>
                {{ props.row.name }}
              </template>
            </template>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  rows: Array<{ id: number; name: string; hasSkill?: boolean }>;
  columns: Array<{ name: string; label: string; field: string | ((row: unknown) => unknown) }>;
  selectedRowId: number | null;
}>();

const emit = defineEmits(['select-row', 'set-selected-row-id']);

const localRows = ref([...props.rows]);

function selectRow(id: number) {
  if (props.selectedRowId === id) {
    emit('set-selected-row-id', null);
  } else {
    emit('set-selected-row-id', id);
    const row = localRows.value.find((row) => row.id === id);
    if (row) emit('select-row', row);
  }
}
</script>

<style scoped>
.custom-table {
  max-height: 400px;
  max-width: 700px !important;
  min-width: 0 !important;
}
.cell-relative {
  position: relative;
}
.delete-icon {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
  z-index: 2;
  size: 2rem;
}
.row-hover:hover .delete-icon {
  opacity: 1;
}
</style>
