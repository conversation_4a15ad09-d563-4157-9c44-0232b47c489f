<template>
  <div class="q-px-lg q-py-md">
    <div class="timeline-container">
      <q-timeline color="secondary" layout="loose" class="custom-timeline">
        <q-timeline-entry
          v-for="(period, idx) in periods"
          :key="period.label"
          :title="period.label"
          icon="fiber_manual_record"
          :color="period.color"
          :side="idx % 2 === 0 ? 'left' : 'right'"
          class="custom-timeline-entry"
          :ref="
            (el) => {
              if (el && typeof el === 'object' && '$el' in el) {
                timelineRefs[idx] = el as ComponentPublicInstance;
              }
            }
          "
        >
          <q-btn
            flat
            round
            dense
            size="sm"
            :icon="expanded[idx] ? 'expand_less' : 'expand_more'"
            class="q-mb-sm"
            @click="toggleDetail(idx)"
          />
          <div v-show="expanded[idx]" class="timeline-detail">
            <div
              v-for="(line, lidx) in period.details"
              :key="lidx"
              class="timeline-detail-line clickable"
              @click="showDetailSkillCard(line, idx)"
            >
              {{ line }}
            </div>
          </div>
        </q-timeline-entry>
      </q-timeline>

      <div
        v-if="dialog && selectedIndex !== null"
        class="skill-card-overlay"
        :class="{
          'skill-card-right': selectedIndex % 2 === 0,
          'skill-card-left': selectedIndex % 2 === 1,
        }"
        :style="skillCardStyle"
      >
        <SkillCard :skill="selectedSkill" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import type { ComponentPublicInstance } from 'vue';
import SkillCard from './SkillCard.vue';

type Competency = {
  label: string;
  color: string;
  textColor: string;
};

type SkillType = {
  id: number;
  name: string;
  description: string;
  competencies: Competency[];
  competencyDetailTitle: string;
  competencyDetail: string;
};

const periods = [
  {
    label: 'อายุการปฏิบัติงาน 1-2 ปี',
    color: 'positive',
    details: [
      'ความรู้และทักษะทั่วไป',
      'การทำงานภายใต้ความและการปฏิบัติงานที่ได้รับมอบหมายจากหัวหน้า',
      'การเขียนบันทึกผลลัพธ์ทางวิชาการ',
      'การพัฒนาทักษะการคิดวิเคราะห์',
      'การทำงานเป็นทีม',
      'การบริหารเวลาและวินัยของตนเอง',
      'กิจกรรมเสริมสร้างทักษะที่เกี่ยวข้อง',
      'การพัฒนาตนเองอย่างต่อเนื่อง',
      'ความตั้งใจในผลงานตามภาระงาน/ภาระหน้าที่และความคาดหวังของหน่วยงาน',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารจัดการห้องสมุด ภาษาอังกฤษ การสอน และประเมินผล',
      'การพัฒนาโครงการวิจัยและบทความทางวิชาการ',
      'การนำเสนอผลงานทางวิชาการ',
      'การใช้โปรแกรมระบบสารสนเทศทางวิชาชีพ',
      'การพัฒนาระบบงานตามมาตรฐานสากล',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 3-5 ปี',
    color: 'primary',
    details: [
      'ความรู้และทักษะทั่วไป',
      'การทำงานแบบมืออาชีพ',
      'การสร้างนวัตกรรมในการปฏิบัติงาน',
      'การแก้ไขปัญหาเชิงสร้างสรรค์',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาตนเองอย่างต่อเนื่อง (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารจัดการห้องสมุด ภาษาอังกฤษ การสอน และประเมินผล (ต่อเนื่อง)',
      'การพัฒนาโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐานสากล (ต่อเนื่อง)',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 6-8 ปี',
    color: 'grey-5',
    details: [
      'ทักษะทั่วไป',
      'การทำงานแบบมืออาชีพ (ต่อเนื่อง)',
      'การแก้ไขปัญหาเชิงสร้างสรรค์ (ต่อเนื่อง)',
      'หลักการทางวิชาการ',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 9 ปีขึ้นไป',
    color: 'grey-5',
    details: [
      'ความรู้และทักษะทั่วไป',
      'หลักการทางวิชาการ (ต่อเนื่อง)',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
    ],
  },
];

const expanded = ref(periods.map(() => true));
const dialog = ref(false);
const selectedIndex = ref<number | null>(null);
const selectedSkill = ref<SkillType>({
  id: 0,
  name: '',
  description: '',
  competencies: [],
  competencyDetailTitle: '',
  competencyDetail: '',
});

const timelineRefs = ref<ComponentPublicInstance[]>([]);

const skillCardStyle = computed(() => {
  if (selectedIndex.value === null || !timelineRefs.value[selectedIndex.value]) {
    return {};
  }

  const timelineEntry = timelineRefs.value[selectedIndex.value];
  if (!timelineEntry?.$el) return {};

  const rect = timelineEntry.$el.getBoundingClientRect();
  const containerRect = timelineEntry.$el.closest('.timeline-container')?.getBoundingClientRect();
  if (!containerRect) return {};

  return {
    top: `${rect.top - containerRect.top}px`,
    height: `${rect.height}px`,
  };
});

function toggleDetail(index: number) {
  expanded.value[index] = !expanded.value[index];

  if (!expanded.value[index] && selectedIndex.value === index) {
    dialog.value = false;
    selectedIndex.value = null;
  }
}

async function showDetailSkillCard(detail: string, index: number) {
  selectedSkill.value = {
    id: Date.now(),
    name: detail,
    description: 'รายละเอียดของ ' + detail,
    competencies: [
      { label: 'การทำงานเป็นทีม', color: 'white', textColor: 'grey-7' },
      { label: 'ความคิดสร้างสรรค์และนวัตกรรม', color: 'white', textColor: 'grey-7' },
      { label: 'การพัฒนาอย่างยั่งยืน', color: 'warning', textColor: 'black' },
    ],
    competencyDetailTitle: 'การพัฒนาอย่างยั่งยืน',
    competencyDetail: 'คำอธิบายสมรรถนะ...',
  };
  selectedIndex.value = index;
  dialog.value = true;

  await nextTick();
}
</script>

<style scoped>
.timeline-container {
  position: relative;
  max-width: 1300px;
  margin: 0 auto;
}

.custom-timeline {
  max-width: 1100px;
  margin: 0 auto;
}

.custom-timeline-entry {
  font-size: 1rem;
}

.timeline-detail {
  background: #fafafa;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 8px;
  margin-top: 8px;
  max-width: 500px;
}

.timeline-detail-line {
  margin-bottom: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.timeline-detail-line:hover {
  background: #e0e0e0;
}

.clickable {
  cursor: pointer;
  color: black;
  font-size: 14px;
  transition: color 0.2s;
}

.skill-card-overlay {
  position: absolute;
  width: 45%;
  z-index: 10;
  max-width: 400px;
  display: flex;
  align-items: center;
}

.skill-card-right {
  right: 220px;
}

.skill-card-left {
  left: 0px;
}

:deep(.q-timeline-entry) {
  margin-bottom: 60px;
}

:deep(.q-timeline-entry:last-child) {
  margin-bottom: 20px;
}
</style>
