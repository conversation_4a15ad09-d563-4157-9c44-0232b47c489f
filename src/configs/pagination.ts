import type { QTableProps } from 'quasar';

export const defaultPaginationValue: QTableProps['pagination'] = {
  sortBy: 'id',
  page: 1,
  rowsPerPage: 10,
  descending: true,
  rowsNumber: 0,
};

export const smallPaginationValue: QTableProps['pagination'] = {
  sortBy: 'id',
  page: 1,
  rowsPerPage: 5,
  descending: false,
  rowsNumber: 0,
};

export const ascendingPaginationValue: QTableProps['pagination'] = {
  sortBy: 'id',
  page: 1,
  rowsPerPage: 10,
  descending: false,
  rowsNumber: 0,
};
