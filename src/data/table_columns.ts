import type { QTableColumn } from 'quasar';
import type { Assessment, Permission, User } from 'src/types/models';
import { formatDateDisplay } from 'src/utils/formatter';

export const quizManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'left',
    field: (row) => row.creator?.name || '-',
    sort: (a, b) => (a.creator?.name || '').localeCompare(b.creator?.name || ''),
    sortable: true,
    style: 'min-width: 150px;',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด - ปิด',
    align: 'left' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center' as const,
    field: 'assessmentLink',
    sortable: false,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const quizUserColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'startAt',
    label: 'เวลาเปิด - ปิด',
    align: 'left' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const evaluateManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'left',
    field: (row) => row.creator?.name || '-',
    sort: (a, b) => (a.creator?.name || '').localeCompare(b.creator?.name || ''),
    sortable: true,
    style: 'min-width: 150px;',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด - ปิด',
    align: 'left',
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center',
    field: (rows: { link: string }) => rows.link ?? '-',
    sortable: false,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center',
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const userColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'รหัส',
    align: 'center',
    field: (row: User) => row.id,
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'name',
    align: 'center',
    label: 'บุคคลากร',
    field: 'name',
    headerStyle: 'width: 200px',
    style: 'text-align: left; max-width: 200px;',
  },
  {
    name: 'username',
    align: 'center',
    label: 'ส่วนงาน',
    field: 'email', // รอเอามาใส่
    headerStyle: 'width: 150px',
    style: 'text-align: left; max-width: 150px;',
  },
  {
    name: 'role',
    align: 'center',
    label: 'บทบาท',
    field: (row: User) => row.roles?.map((role) => role.name).join(', ') || '-',
    headerStyle: 'width: 150px',
    style: 'text-align: left; max-width: 150px;',
  },
  {
    name: 'actions',
    align: 'center',
    label: 'เครื่องมือ',
    field: '',
    headerStyle: 'width: 150px',
    style: 'text-align: center; max-width: 150px;',
  },
];

export const roleColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'รหัส',
    align: 'center',
    field: (row: User) => row.id,
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'name',
    align: 'center',
    label: 'บทบาท',
    field: 'name',
    headerStyle: 'width: 150px',
    style: 'text-align: left; max-width: 150px;',
  },
  {
    name: 'description',
    align: 'center',
    label: 'รายละเอียด',
    field: 'description',
    headerStyle: 'width: 200px',
    style: 'text-align: left; max- width: 200px;',
  },
  {
    name: 'departmentName',
    align: 'center',
    label: 'ส่วนงาน',
    field: (row: User) => {
      const id = row.id;
      if (id === 1) return '-';
      if (id === 2) return 'กบกพบ.';
      return 'ส่วนงาน';
    },
    headerStyle: 'width: 80px',
    style: 'text-align: center; max-width: 80px;',
  },
  {
    name: 'userCount',
    align: 'center',
    label: 'จำนวน',
    field: () => Math.floor(Math.random() * 10) + 1, // Mock data: random 1-10
    headerStyle: 'width: 80px',
    style: 'text-align: center; max-width: 80px;',
  },
  {
    name: 'actions',
    align: 'center',
    label: 'เครื่องมือ',
    field: '',
    headerStyle: 'width: 150px',
    style: 'text-align: center; max-width: 150px;',
  },
];

export const permColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'left',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'left',
    label: 'ชื่อ',
    field: 'name',
  },
  {
    name: 'nameEn',
    align: 'left',
    label: 'ชื่ออังกฤษ',
    field: 'nameEn',
  },
  {
    name: 'status',
    align: 'left',
    label: 'สถานะ',
    field: (row: Permission) => (row.status ? 'ใช้งาน' : 'ไม่ใช้งาน'),
  },
  {
    name: 'default',
    align: 'left',
    label: 'ค่าเริ่มต้น',
    field: (row: Permission) => (row.isDefault ? 'ใช่' : 'ไม่ใช่'),
  },
  { name: 'actions', align: 'center', label: 'เครื่องมือ', field: '' },
];

export const competencyManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  {
    name: 'competency',
    label: 'ชื่อสมรรถนะ',
    align: 'left' as const,
    field: 'name',
    sortable: true,
    style: 'min-width: 200px; white-space: normal;',
  },
  {
    name: 'description',
    label: 'รายละเอียด',
    align: 'left' as const,
    field: 'description',
    sortable: true,
    style: 'min-width: 300px; white-space: normal;',
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

export const skillManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  {
    name: 'skill',
    label: 'ชื่อความรู้และทักษะ',
    align: 'left' as const,
    field: 'name',
    sortable: true,
    style: 'min-width: 200px; white-space: normal;',
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

export const IDP_DEVELOPMENT_PLANSManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'center' as const, field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแผนพัฒนา',
    align: 'center' as const,
    field: 'name',
    sortable: true,
    style: 'text-align: left;',
  },
  {
    name: 'is_active',
    label: 'สถานะ',
    align: 'center' as const,
    field: 'is_active',
    sortable: true,
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

export const individualUserColumn = <QTableColumn[]>[
  {
    name: 'name',
    label: 'ชื่อ',
    align: 'center' as const,
    field: 'name',
    headerStyle: 'width: 500px',
  },
  {
    name: 'department',
    label: 'คณะ/ส่วนงาน',
    align: 'center' as const,
    field: 'department',
  },
  {
    name: 'position',
    label: 'ตำแหน่ง',
    align: 'center' as const,
    field: 'position',
  },
  {
    name: 'workAge',
    label: 'อายุงาน',
    align: 'center' as const,
    field: 'workAge',
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: '',
  },
];

export const programUserMonitorColumns = <QTableColumn[]>[
  {
    name: 'programName',
    label: 'โครงการที่เข้าร่วม',
    align: 'center' as const,
    field: 'programName',
    headerStyle: 'width: 600px;',
    style: 'text-align: left; max-width: 600px; white-space: normal;',
  },
  {
    name: 'joinDate',
    label: 'วันที่เข้าร่วม',
    align: 'center' as const,
    field: (row) => formatDateDisplay(row.joinDate),
  },
  {
    name: 'preTestScore',
    label: 'คะแนน Pre-Test',
    align: 'center' as const,
    field: 'preTestScore',
  },
  {
    name: 'postTestScore',
    label: 'คะแนน Post-Test',
    align: 'center' as const,
    field: 'postTestScore',
  },
  {
    name: 'satisfactionSurvey',
    label: 'แบบประเมินความพึงพอใจ',
    align: 'center' as const,
    field: 'satisfactionSurvey',
  },
  {
    name: 'evaluationResult',
    label: 'ผลประเมิน',
    align: 'center' as const,
    field: 'evaluationResult',
  },
];

export const generalSkillColumns = <QTableColumn[]>[
  {
    name: 'name',
    label: 'ความรู้และทักษะทั่วไป',
    align: 'center' as const,
    field: 'name',
    headerStyle: 'width: 400px',
    style: 'text-align: left; max-width: 400px; white-space: normal;',
  },
];

export const academicSkillColumns = <QTableColumn[]>[
  {
    name: 'name',
    label: 'ความรู้และทักษะเฉพาะด้านวิชาการ',
    align: 'center' as const,
    field: 'name',
    headerStyle: 'width: 400px',
    style: 'text-align: left; max-width: 400px; white-space: normal;',
  },
];

export const FollowUpColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center' as const,
    field: 'id',
    sortable: true,
    headerStyle: 'width: 80px',
    style: 'text-align: center; max-width: 80px;',
  },
  {
    name: 'name',
    label: 'ชื่อความรู้และทักษะ',
    align: 'left' as const,
    field: 'name',
    headerStyle: 'width: 400px',
    style: 'text-align: left; max-width: 400px; white-space: normal;',
  },
  {
    name: 'actions',
    label: 'หลักฐาน',
    align: 'center' as const,
    field: '',
    sortable: false,
    headerStyle: 'width: 100px',
    style: 'text-align: center; max-width: 100px;',
  },
  {
    name: 'is_active',
    label: 'สถานะ',
    align: 'center' as const,
    field: 'is_active',
    sortable: false,
    headerStyle: 'width: 120px',
    style: 'text-align: center; max-width: 120px;',
  },
];

export const AssignUserToRoleColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'name',
    label: 'ชื่อ-นามสกุล',
    align: 'center',
    field: 'name',
    sortable: false,
    headerStyle: 'width: 200px',
    style: 'text-align: left; max-width: 200px;',
  },
  {
    name: 'actions',
    label: 'เลือก',
    align: 'center',
    field: () => '',
    sortable: false,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
];

export const AssignPermToRoleColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'name',
    label: 'รายการสิทธิ์อนุญาต',
    align: 'center',
    field: 'name',
    sortable: false,
    headerStyle: 'width: 200px',
    style: 'text-align: left; max-width: 200px;',
  },
  {
    name: 'actions',
    label: 'สถานะ',
    align: 'center',
    field: () => '',
    sortable: false,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
];

export const AssignRoleToUserColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'name',
    label: 'บทบาท',
    align: 'center',
    field: 'name',
    sortable: false,
    headerStyle: 'width: 200px',
    style: 'text-align: left; max-width: 200px;',
  },
  {
    name: 'actions',
    label: 'เลือก',
    align: 'center',
    field: () => '',
    sortable: false,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
];

export const AssignDepartmentsToUserColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
  {
    name: 'nameTh',
    label: 'ส่วนงาน',
    align: 'center',
    field: 'nameTh',
    sortable: false,
    headerStyle: 'width: 200px',
    style: 'text-align: left; max-width: 200px;',
  },
  {
    name: 'actions',
    label: 'เลือก',
    align: 'center',
    field: () => '',
    sortable: false,
    headerStyle: 'width: 50px',
    style: 'text-align: center; max-width: 50px;',
  },
];
export const SkillEvaluationColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    align: 'center' as const,
    field: 'id',
    sortable: true,
    headerStyle: 'width: 80px',
    style: 'text-align: center; max-width: 80px;',
  },
  {
    name: 'name',
    label: 'ชื่อความรู้และทักษะ',
    align: 'left' as const,
    field: 'name',
    sortable: false,
    headerStyle: 'width: 300px',
    style: 'text-align: left; max-width: 300px; white-space: normal;',
  },
  {
    name: 'type',
    label: 'ประเภททักษะ',
    align: 'center' as const,
    field: 'type',
    sortable: false,
    headerStyle: 'width: 120px',
    style: 'text-align: center; max-width: 120px;',
  },
  {
    name: 'way',
    label: 'วิธีที่ได้รับ',
    align: 'center' as const,
    field: 'way',
    sortable: false,
    headerStyle: 'width: 150px',
    style: 'text-align: center; max-width: 150px;',
  },
  {
    name: 'date',
    label: 'วันที่รับรอง',
    align: 'center' as const,
    field: 'date',
    sortable: false,
    headerStyle: 'width: 120px',
    style: 'text-align: center; max-width: 120px;',
  },
  {
    name: 'is_pass',
    label: 'ผลการประเมิน',
    align: 'center' as const,
    field: 'is_pass',
    sortable: false,
    headerStyle: 'width: 150px',
    style: 'text-align: center; max-width: 150px;',
  },
];
