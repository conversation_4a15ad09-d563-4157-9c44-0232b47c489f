<script setup lang="ts">
import Calendar from 'src/components/common/CalendarCom.vue';
import { useAuthStore } from '../stores/auth';
import type { User } from '../types/models';
import { ref, onMounted } from 'vue';
import type { CalendarEvent } from 'src/types/calendar.ts';

const authStore = useAuthStore();
const user = ref<User | undefined>(authStore.getCurrentUser());
const allcalendar = ref(false)

const selectedDayEvents = ref<CalendarEvent[] | null>(null)
const selectedDayLabel = ref<string | null>(null)

// Sample activities data
const activities = ref([
  {
    id: 1,
    title: 'ลงทะเบียนเข้าร่วม "การใช้โปรแกรมสำหรับจัดทำเอกสารกรมวิชา"',
    time: '16 พฤษภาคม 2568, 09:32 น.'
  },
  {
    id: 2,
    title: 'เข้าร่วมกิจกรรมอบรม "การจัดทำการวิเคราะห์ความเสี่ยงในกิจกรรมการทำงาน การคิดให้ครอบคลุม และการประเมินผล"',
    time: '3 พฤษภาคม 2568, 14:24 น.'
  },
  {
    id: 3,
    title: 'เข้าร่วมกิจกรรมอบรม "การสร้างสื่อการเรียนการสอน"',
    time: '2 พฤษภาคม 2568, 13:30 น.'
  },
  {
    id: 4,
    title: 'ลงทะเบียนเข้าร่วม "การใช้โปรแกรมสำหรับจัดทำเอกสารกรมวิชา"',
    time: '16 พฤษภาคม 2568, 09:32 น.'
  },
  {
    id: 5,
    title: 'เข้าร่วมกิจกรรมอบรม "การจัดทำการวิเคราะห์ความเสี่ยงในกิจกรรมการทำงาน การคิดให้ครอบคลุม และการประเมินผล"',
    time: '3 พฤษภาคม 2568, 14:24 น.'
  }
]);

const stats = ref({
  activities: { current: 10, total: 24 },
  hours: { current: 12, total: 25 },
  certificates: { current: 2, total: 5 }
});

onMounted(() => {
  user.value = authStore.getCurrentUser();
});

const navigateToTraining = () => {
  console.log('Navigate to training system');
};



const applyForSpecificTraining = (event: CalendarEvent) => {
  console.log('Apply for specific training:', event);
};

const AllTraining = () => {
  allcalendar.value = !allcalendar.value;
};

const onDayClicked = (events: CalendarEvent[], date: string) => {
  console.log('🏠 HomePage received click');
  console.log('📅 Date:', date);
  console.log('🎯 Events:', events);
  console.log('📊 Events length:', events?.length || 0);

  // ตรวจสอบว่าข้อมูลถูกต้องหรือไม่
  if (!events || !Array.isArray(events)) {
    console.error('❌ Invalid events data:', events);
    return;
  }

  selectedDayEvents.value = events;
  selectedDayLabel.value = date;

  console.log('✅ State updated:');
  console.log('- selectedDayEvents:', selectedDayEvents.value);
  console.log('- selectedDayLabel:', selectedDayLabel.value);
}
</script>

<template>
  <q-page padding>
    <div class="q-mb-lg">
      <h1 class="text-h4 q-mb-md">สวัสดี, {{ user?.name || 'จักริน สุขสวัสดิ์ชน' }}!</h1>
      <p class="text-subtitle1 q-mb-md">กรุณาเลือกระบบเพื่อเริ่มใช้งาน</p>

      <div class="row q-col-gutter-md">
        <!-- Calendar Section -->
        <div class="col-12 col-md-8">
          <Calendar @day-clicked="onDayClicked" />

          <q-btn class="apply-btn q-mt-md" color="warning" @click="AllTraining" icon="event" style="max-width: 300px;"
            :label="allcalendar ? 'ซ่อน Action Plan' : 'Action Plan ทั้งหมด'" />

          <Calendar style="margin-top: 20px;" v-if="allcalendar == true" />

          <div class="activities-card q-pa-md q-mb-md">
            <h6 class="section-title">กิจกรรมล่าสุด</h6>

            <div class="activities-list">
              <div v-for="activity in activities" :key="activity.id" class="activity-item q-mb-sm">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-4">
          <div class="sidebar-container">
            <div class="welcome-card q-pa-md q-mb-md">
              <h6 class="welcome-title">สวัสดี, จักริน สุขสวัสดิ์ชน</h6>
              <p class="welcome-subtitle">ยินดีต้อนรับสู่ระบบการพัฒนาบุคลากร มหาวิทยาลัยบูรพา</p>
              <q-btn class="welcome-btn full-width" color="warning" @click="navigateToTraining">
                เข้าสู่ระบบอบรม
              </q-btn>
            </div>

            <div class="training-card q-pa-md q-mb-md" v-if="selectedDayEvents && selectedDayEvents.length > 0">
              <h6 class="section-title">
                {{ selectedDayLabel ? 'กิจกรรมวันที่ ' + selectedDayLabel : 'โครงการที่คุณสามารถเข้าร่วมได้' }}
              </h6>

              <template v-if="selectedDayEvents && selectedDayEvents.length > 0">
                <div v-for="event in selectedDayEvents" :key="event.id" style="margin-top: 40px;">
                  <div class="training-item">
                    <div class="training-icon">
                      <q-icon name="book" size="md" />
                    </div>
                    <div class="training-content">
                      <div class="training-id">ชื่อหัวข้อ IC-{{ String(event.id).padStart(3, '0') }}</div>
                      <div class="training-name">{{ event.name }}</div>
                      <div class="training-location">ที่ตั้ง: คณะเทคโนโลยีการเกษตรและอาหารอุตสาหกรรม</div>
                      <div class="training-datetime" v-if="event.start == event.end">วันที่ {{ event.start }}</div>
                      <div class="training-datetime" v-if="event.start != event.end">วันที่ {{ event.start }} - {{
                        event.end }}</div>
                    </div>
                  </div>

                  <q-btn class="apply-btn full-width q-mt-md" color="warning" @click="applyForSpecificTraining(event)">
                    สมัครเข้าร่วม
                  </q-btn>
                </div>
              </template>

            </div>



            <div class="stats-grid">
              <div class="stats-grid-custom">
                <div class="stat-card">
                  <div class="stat-number">{{ stats.activities.current }} / {{ stats.activities.total }}</div>
                  <div class="stat-label">จำนวนกิจกรรมที่เข้าร่วม</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number">{{ stats.hours.current }} / {{ stats.hours.total }}</div>
                  <div class="stat-label">จำนวนชั่วโมงการอบรม</div>
                </div>

                <div class="stat-card full-width">
                  <div class="stat-number">{{ stats.certificates.current }} / {{ stats.certificates.total }}</div>
                  <div class="stat-label">จำนวนประกาศนียบัตร</div>
                </div>
              </div>
            </div>





          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
}

.welcome-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.welcome-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.4;
}

.welcome-btn {
  font-weight: 600;
}

.training-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activities-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  max-width: 1000px;
  margin-top: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: -2px;
}

.training-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.training-item-wrapper {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.training-item-wrapper:not(:last-of-type) {
  margin-bottom: 12px;
}

.training-item-wrapper .training-item {
  margin-bottom: 0;
}

.training-icon {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.training-content {
  flex: 1;
}

.training-id {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.training-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.3;
}

.training-location {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.training-datetime {
  font-size: 12px;
  color: #999;
}

.apply-btn {
  font-weight: 600;
}

.activities-list {
  max-height: 270px;
  overflow-y: auto;
}

.activity-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.activity-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 11px;
  color: #666;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.stat-card {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e0e0e0;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .stat-card {
    padding: 12px;
  }

  .stat-number {
    font-size: 14px;
  }

  .stat-label {
    font-size: 10px;
  }

  /* ปรับ z-index สำหรับ mobile menu */
  .q-page {
    position: relative;
    z-index: 1;
  }
}

/* Scrollbar styling */
.activities-list::-webkit-scrollbar {
  width: 4px;
}

.activities-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.activities-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.activities-list::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.stats-grid-custom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.full-width {
  grid-column: span 2;
}
</style>
