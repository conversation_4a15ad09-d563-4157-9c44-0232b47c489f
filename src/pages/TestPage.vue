<script setup lang="ts">
import { useQuasar } from 'quasar';
import CreateIdpTabPlan from 'src/components/idp/tab-plan/CreateIdpTabPlan.vue';
import type { IdpTabs } from 'src/types/idp-tab-plan';

const $q = useQuasar();

function openDialog(tabType: IdpTabs = 'common-i') {
  $q.dialog({
    component: CreateIdpTabPlan,
    componentProps: {
      tabs: tabType,
      form: {
        ageWork: '1-2YEAR',
        skills: [],
      },
    },
    persistent: true,
  })
    .onOk((data) => {
      console.log('Form submitted:', data);
    })
    .onCancel(() => {
      console.log('Dialog cancelled');
    });
}
</script>

<template>
  <q-page class="flex flex-col items-center justify-center q-gutter-md">
    <div class="text-h5 q-mb-md">Test IDP Tab Plan Components</div>

    <div class="column q-gutter-md">
      <q-btn
        @click="openDialog('common-i')"
        label="Common I - ข้าราชการระดับต้น"
        rounded
        color="blue"
        :style="{ width: '350px', height: '50px' }"
      />

      <q-btn
        @click="openDialog('common-m')"
        label="Common M - ข้าราชการระดับกลาง"
        rounded
        color="green"
        :style="{ width: '350px', height: '50px' }"
      />

      <q-btn
        @click="openDialog('support')"
        label="Support - เลขาธิการ"
        rounded
        color="purple"
        :style="{ width: '350px', height: '50px' }"
      />

      <q-btn
        @click="openDialog('manager')"
        label="Manager - สายงานสนับสนุนวิชาการ"
        rounded
        color="teal"
        :style="{ width: '350px', height: '50px' }"
      />

      <q-btn
        @click="openDialog('career')"
        label="Career - สายงานวิชาการ"
        rounded
        color="orange"
        :style="{ width: '350px', height: '50px' }"
      />

      <q-btn
        @click="openDialog('specialized')"
        label="Specialized - ผู้บริหารเลขาธิการ"
        rounded
        color="pink"
        :style="{ width: '350px', height: '50px' }"
      />
    </div>
  </q-page>
</template>
