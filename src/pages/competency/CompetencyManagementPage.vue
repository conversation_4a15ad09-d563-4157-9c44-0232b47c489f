<template>
  <q-page padding class="q-gutter-y-md">
    <TopHeaderTable
      title="จัดการสมรรถนะ"
      create-button-label="เพิ่ม"
      pageType="competency"
      @search="onSearchUpdate"
      @create="onClickAdd"
      :show-create-button="showCreateButton"
      :subtitle="selectedTabLabel"
    >
      <template #tab>
        <TabNavigation class="q-mt-sm" :tabs="tabItems" v-model="selectedTab" />
      </template>
    </TopHeaderTable>

    <q-table
      :rows="rows"
      :columns="competencyManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              @click="onClickView(row)"
            />
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="edit"
              @click="onClickEdit(row)"
              style="background-color: #303f9f"
            />
            <q-btn
              dense
              unelevated
              class="view-icon delete-btn"
              icon="delete"
              @click="onClickDelete(row)"
              color="negative"
            />
          </div>
        </q-td>
      </template>
    </q-table>
    <DeleteConfirmDialog
      v-model="showDeleteDialog"
      :title="'ยืนยันการลบ'"
      :message="'คุณต้องการลบข้อมูลนี้หรือไม่?'"
      @confirm="handleDeleteConfirm"
      @cancel="showDeleteDialog = false"
    />
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { defineAsyncComponent, ref, watch, onMounted, computed } from 'vue';
import { competencyManagementColumns } from 'src/data/table_columns';
import type { Competency } from 'src/types/models';
import { useCompetencyStore } from 'src/stores/competencies';

// import components
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import DeleteConfirmDialog from 'src/components/common/DeleteConfirmDialog.vue';

const $q = useQuasar();

const competencyStore = useCompetencyStore();

// ---------------------------
// Tab Setup
const tabItems = [
  { label: 'สมรรถนะหลัก', value: 'สมรรถนะหลัก' },
  { label: 'สมรรถนะสายวิชาการ', value: 'สมรรถนะสายวิชาการ' },
  { label: 'สมรรถนะสายสนับสนุนวิชาการ', value: 'สมรรถนะสายสนับสนุนวิชาการ' },
  { label: 'สมรรถนะทางการบริหาร', value: 'สมรรถนะทางการบริหาร' },
];

const TAB_KEY = 'selected-competency-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'สมรรถนะหลัก');
const filteredRowsByTab = ref<Competency[]>([]);
const searchKeyword = ref('');

// Add refs for dialog state and row to delete
const showDeleteDialog = ref(false);
const rowToDelete = ref<Competency | null>(null);

// ---------------------------
// Filtering logic
const filterCompetenciesByTab = async (careerType: string) => {
  await competencyStore.fetchCompetencies({
    sortBy: 'name',
    descending: false,
    page: 1,
    rowsPerPage: 100
  }, careerType);
  filteredRowsByTab.value = competencyStore.competencies;
};

watch(selectedTab, async (val) => {
  localStorage.setItem(TAB_KEY, val);
  await filterCompetenciesByTab(val);
});
onMounted(async () => {
  await filterCompetenciesByTab(selectedTab.value);
});

const selectedTabLabel = computed(() => {
  const tab = tabItems.find(item => item.value === selectedTab.value);
  return tab ? tab.label : '';
});

const showCreateButton = computed(() => true);
// ---------------------------
// Computed rows with search
const rows = computed(() => {
  const competencies = competencyStore.competencies;
  if (!searchKeyword.value) return competencies;

  const keyword = searchKeyword.value.toLowerCase();
  return competencies.filter(
    (item) =>
      item.name?.toLowerCase().includes(keyword) ||
      item.description?.toLowerCase().includes(keyword) ||
      item.career_type?.toLowerCase().includes(keyword),
  );
});// ---------------------------
// Actions
const onClickEdit = (row: Competency) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/competency/CompetencyForm.vue')),
    componentProps: {
      title: 'แก้ไขสมรรถนะ',
      formData: row,
      career_type: row.career_type,
    },
    persistent: true,
  }).onOk((data: Competency) => {
    void (async () => {
      try {
        if (!data) {
          console.error('No data received from form');
          return;
        }

        if (!row.id) {
          throw new Error('Competency ID is undefined');
        }

        const updatedData = {
          name: data.name,
          description: data.description || '',
          career_type: data.career_type,
        };

        await competencyStore.updateCompetency(row.id, updatedData);
        await filterCompetenciesByTab(selectedTab.value);

        $q.notify({
          type: 'positive',
          message: 'แก้ไขสมรรถนะสำเร็จ',
          position: 'bottom'
        });
      } catch (error) {
        console.error('Error updating competency:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการแก้ไขสมรรถนะ',
          position: 'bottom'
        });
      }
    })();
  });
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/competency/CompetencyForm.vue')),
    componentProps: { title: 'สร้างสมรรถนะใหม่' },
    persistent: true,
  }).onOk((data: Competency) => {
    // ใช้ void function wrapper แทน async
    void (async () => {
      try {
        // ตรวจสอบว่า data มีค่าและถูกต้อง
        if (!data) {
          console.error('No data received from form');
          return;
        }

        // เพิ่ม comp_type และ career_type ตาม selectedTab
        const enhancedData = {
          name: data.name,
          description: data.description || '',
          career_type: data.career_type,
        };

        await competencyStore.addCompetency(enhancedData);

        // รอให้ add เสร็จก่อน แล้วค่อย refresh
        await filterCompetenciesByTab(selectedTab.value);

        // แสดง success message
        $q.notify({
          type: 'positive',
          message: 'เพิ่มสมรรถนะสำเร็จ',
          position: 'bottom'
        });
      } catch (error) {
        console.error('Error adding competency:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการเพิ่มสมรรถนะ',
          position: 'bottom'
        });
      }
    })();
  });
};

const onClickDelete = (row: Competency) => {
  rowToDelete.value = row;
  showDeleteDialog.value = true;
};

const handleDeleteConfirm = () => {
  if (!rowToDelete.value || !rowToDelete.value.id) {
    $q.notify({
      type: 'negative',
      message: 'Competency ID is undefined',
      position: 'bottom'
    });
    showDeleteDialog.value = false;
    return;
  }
  competencyStore.deleteCompetency(rowToDelete.value.id)
    .then(() => filterCompetenciesByTab(selectedTab.value))
    .then(() => {
      $q.notify({
        type: 'positive',
        message: 'ลบสมรรถนะสำเร็จ',
        position: 'bottom'
      });
    })
    .catch((error) => {
      console.error('Error deleting competency:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการลบสมรรถนะ',
        position: 'bottom'
      });
    })
    .finally(() => {
      showDeleteDialog.value = false;
      rowToDelete.value = null;
    });
};

const onClickView = (row: Competency) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/competency/CompetencyForm.vue')),
    componentProps: {
      title: 'ดูรายละเอียดสมรรถนะ',
      formData: row,
      readOnly: true
    },
    persistent: true,
  });
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
};
</script>

<style scoped>
.disabled-btn-gray {
  background-color: #a0a0a0 !important;
}
.disabled-btn-gray .q-icon {
  opacity: 0.6;
}

</style>
