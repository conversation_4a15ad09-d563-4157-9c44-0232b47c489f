<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการแผนพัฒนาบุคลากร"
      create-button-label="เพิ่ม"
      @search="onSearchUpdate"
      @create="onClickAdd"
    />

    <q-table
      :rows="rows"
      :columns="IDP_DEVELOPMENT_PLANSManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="loading"
      :pagination="pagination"
      @request="onRequest"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn dense unelevated class="view-icon" icon="build" @click="onClickPlanning(row)" />
            <q-btn dense unelevated class="view-icon" icon="visibility" />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
      <template v-slot:body-cell-is_active="{ row }">
        <q-td class="text-center">
          <StatusCapsule :published="row.isActive" />
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar, type QTableProps } from 'quasar';
import { useRouter } from 'vue-router';
import { IDP_DEVELOPMENT_PLANSManagementColumns } from 'src/data/table_columns';
import type { DevelopmentPlan } from 'src/types/idp';
import { defineAsyncComponent, ref, onMounted } from 'vue';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import { useDevPlansService } from 'src/services/idp/devPlansService';

// Reactive data
const rows = ref<DevelopmentPlan[]>([]);
const loading = ref(false);
const searchKeyword = ref('');

// Pagination configuration
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

const $q = useQuasar();
const router = useRouter();

// Initialize service
const devPlansService = useDevPlansService();

const fetchDevelopmentPlans = async (requestProp?: { pagination: QTableProps['pagination'] }) => {
  loading.value = true;
  try {
    const paginationToUse = requestProp?.pagination || pagination.value;
    const response = await devPlansService.getAll(paginationToUse, searchKeyword.value, 'central');

    rows.value = response.data;
    pagination.value.rowsNumber = response.meta.total;

    if (requestProp?.pagination) {
      pagination.value.page = requestProp.pagination.page || 1;
      pagination.value.rowsPerPage = requestProp.pagination.rowsPerPage || 10;
      pagination.value.sortBy = requestProp.pagination.sortBy || 'id';
      pagination.value.descending = requestProp.pagination.descending || false;
    }
  } catch (error) {
    // Error handling is done in the service
    console.error('Failed to fetch development plans:', error);
  } finally {
    loading.value = false;
  }
};

// Table request handler
const onRequest = (props: { pagination: QTableProps['pagination'] }) => {
  void fetchDevelopmentPlans(props);
};

// Event handlers
const onClickPlanning = async (row: DevelopmentPlan) => {
  await router.push({ name: 'dev-plan-planning', params: { id: row.id } });
};

const onClickEdit = (row: DevelopmentPlan) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/idp/IdpForm.vue')),
    componentProps: {
      title: 'แก้ไขแผน',
      formData: row,
    },
    persistent: true,
  })
    .onOk((data: DevelopmentPlan) => {
      void (async () => {
        try {
          await devPlansService.update(row.id, data);
          void fetchDevelopmentPlans();
        } catch (error) {
          console.error('Failed to update development plan:', error);
        }
      })();
    })
    .onCancel(() => {
      console.log('Edit dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Edit dialog dismissed');
    });
};

const onClickDelete = (row: DevelopmentPlan) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณต้องการลบแผนพัฒนา "${row.name}" หรือไม่?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void (async () => {
      try {
        await devPlansService.remove(row.id);
        void fetchDevelopmentPlans();
      } catch (error) {
        console.error('Failed to delete development plan:', error);
      }
    })();
  });
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(
      () => import('src/components/idp/tab-plan/CreateIdpTabPlan.vue'),
    ),
    componentProps: {
      tabs: 'common-i',
    },
    persistent: true,
  })
    .onOk((data: DevelopmentPlan) => {
      void (async () => {
        try {
          await devPlansService.create(data);
          void fetchDevelopmentPlans();
        } catch (error) {
          console.error('Failed to create development plan:', error);
        }
      })();
    })
    .onCancel(() => {
      console.log('Add dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Add dialog dismissed');
    });
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
  pagination.value.page = 1; // Reset to first page when searching
  void fetchDevelopmentPlans();
};

// Initialize data on component mount
onMounted(() => {
  void fetchDevelopmentPlans();
});
</script>
