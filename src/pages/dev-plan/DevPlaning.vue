<template>
  <q-page padding>
    <div v-if="loading" class="text-center">
      <q-spinner size="lg" />
      <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
    </div>
    <div v-else-if="developmentPlan">
      <div class="text-h5 q-mb-md text-weight-bold">
        จัดการแผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="text-body1 q-mb-md text-weight-bold">
        แผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">สถานะ :</span>
        <StatusCapsule :published="developmentPlan.isActive" class="q-mr-sm" />
        <q-btn class="q-ml-sm bg-black text-white">เผยแพร่</q-btn>
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="หน่วยงาน/ส่วนงาน" :options="searchOptions" />
      </div>
      <div v-if="selectedTab === 'position'" class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="ประเภทสายงาน" :options="searchTypeOptions" />
        <SearchDropDownBar placeholder="ตำแหน่ง" :options="searchPositionOptions" />
        <SearchDropDownBar placeholder="ระดับ" :options="searchRankOptions" />
      </div>
      <div class="q-mb-md">
        <TabNavigation
          v-model="selectedTab"
          :tabs="devPlanTabs.map((tab) => ({ label: tab.label, value: tab.name }))"
          class="q-mb-md"
        />
      </div>
    </div>
    <div v-else class="text-center text-grey-6">ไม่พบข้อมูลแผนพัฒนา</div>

    <div v-if="!loading && developmentPlan">
      <div v-if="selectedTab === 'all'">
        <div class="q-mb-md">
          <div class="row items-center justify-between q-mb-md">
            <span class="text-weight-bold">ความรู้และทักษะทั่วไปของบุคลากร</span>
            <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
          </div>
          <div v-if="categorizedData['all']?.length">
            <div v-for="(typePlan, index) in categorizedData['all']" :key="index" class="q-mb-md">
              <CompetencyListSection
                :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
                :items="typePlan.skills?.map((skill: any) => skill.name) || []"
                v-model="isListOpen"
                @remove="(idx: number) => removeSkill('all', index, idx)"
              />
            </div>
          </div>
          <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
        </div>
      </div>
      <div v-else-if="selectedTab === 'admin'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะทั่วไปของผู้บริหาร</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <div v-if="categorizedData['admin']?.length">
          <div v-for="(typePlan, index) in categorizedData['admin']" :key="index" class="q-mb-md">
            <CompetencyListSection
              :title="`${typePlan.name}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAdmin"
              @remove="(idx: number) => removeSkill('admin', index, idx)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'academic'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายวิชาการ</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <div v-if="categorizedData['academic']?.length">
          <div
            v-for="(typePlan, index) in categorizedData['academic']"
            :key="index"
            class="q-mb-md"
          >
            <CompetencyListSection
              :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAcademic"
              @remove="(idx: number) => removeSkill('academic', index, idx)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'support'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายสนับสนุน</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <div v-if="categorizedData['support']?.length">
          <div v-for="(typePlan, index) in categorizedData['support']" :key="index" class="q-mb-md">
            <CompetencyListSection
              :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenSupport"
              @remove="(idx: number) => removeSkill('support', index, idx)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'adminonly'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านผู้บริหาร</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <div v-if="categorizedData['adminonly']?.length">
          <div
            v-for="(typePlan, index) in categorizedData['adminonly']"
            :key="index"
            class="q-mb-md"
          >
            <CompetencyListSection
              :title="`${typePlan.position?.name || 'ตำแหน่ง'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAdminOnly"
              @remove="(idx: number) => removeSkill('adminonly', index, idx)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'position'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านของตำแหน่ง</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <div v-if="categorizedData['position']?.length">
          <div
            v-for="(typePlan, index) in categorizedData['position']"
            :key="index"
            class="q-mb-md"
          >
            <CompetencyListSection
              :title="`${typePlan.position?.name || 'ตำแหน่ง'} - ${typePlan.ageWork?.name || 'ทั่วไป'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpen"
              @remove="(idx: number) => removeSkill('position', index, idx)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else>
        <div class="q-pa-md bg-grey-3 q-mb-md">เนื้อหาอื่น ๆ (mock)</div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import CompetencyListSection from 'src/components/competency/CompetencyListSection.vue';
import { useQuasar } from 'quasar';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import { useRoute } from 'vue-router';
import type { DevelopmentPlan, TypePlan } from 'src/types/idp';
import { useGlobalStore } from 'src/stores/global';
import SearchDropDownBar from 'src/components/SearchDropDownBar.vue';
import { useDevPlansService } from 'src/services/idp/devPlansService';

// Initialize service
const devPlansService = useDevPlansService();

import { devPlanTabs } from 'src/data/devPlanTabs';

import TabNavigation from 'src/components/common/TabNavigation.vue';

// Mock options for SearchDropDownBar
const searchOptions = [
  'กองบริหารและพัฒนาทรัพยากรบุคคล',
  'สํานักคอมพิวเตอร์',
  'กองแผนงาน',
  'กองกิจการนิสิต',
];

const searchTypeOptions = ['ทุกสายงาน', 'สายวิชาการ', 'สายสนับสนุนวิชาการ'];

const searchPositionOptions = [
  'ทุกตําแหน่ง',
  'นักวิชาการเงินและบัญชี',
  'นักวิชาการศึกษา',
  'นักวิชาการคอมพิวเตอร์',
  'นักวิชาการพัสดุ',
];

const searchRankOptions = ['ปฏิบัติการ', 'ชํานาญการ', 'ชํานาญการพิเศษ', 'ทรงคุณวุฒิ'];

const selectedTab = ref(devPlanTabs[0]?.name ?? '');
const isListOpen = ref(true);
const isListOpenAdmin = ref(true);
const isListOpenAcademic = ref(true);
const isListOpenSupport = ref(true);
const isListOpenAdminOnly = ref(true);
const $q = useQuasar();

// Function to remove skill from categorized data
function removeSkill(tabName: string, typePlanIndex: number, skillIndex: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบทักษะนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const typePlans = categorizedData.value[tabName];
    if (typePlans && typePlans[typePlanIndex]?.skills) {
      typePlans[typePlanIndex].skills.splice(skillIndex, 1);
    }
  });
}

// ตัวอย่าง mock tab name ที่รองรับ overview, plan, result
// devPlanTabs ในที่นี้ควรมี name: 'overview', 'plan', 'result' อย่างน้อย 1 อัน

const route = useRoute();
const globalStore = useGlobalStore();
const loading = ref(false);
const developmentPlan = ref<DevelopmentPlan | null>(null);

// Mapping between TypePlan names and tab names
const typePlanToTabMapping = {
  ทั่วไปบุคลากร: 'all',
  ทั่วไปผู้บริหาร: 'admin',
  เฉพาะด้านวิชาการ: 'academic',
  เฉพาะสายสนับสนุน: 'support',
  เฉพาะด้านบริหาร: 'adminonly',
  ตำแหน่ง: 'position',
};

// Computed properties to categorize data by tabs
const categorizedData = computed(() => {
  if (!developmentPlan.value?.typePlans) {
    return {};
  }

  const categories: Record<string, TypePlan[]> = {};

  developmentPlan.value.typePlans.forEach((typePlan) => {
    const tabName = typePlanToTabMapping[typePlan.name];
    if (tabName) {
      if (!categories[tabName]) {
        categories[tabName] = [];
      }
      categories[tabName].push(typePlan);
    }
  });

  return categories;
});

// Function to extract year from plan name
const extractYearFromPlanName = (planName: string): string => {
  const yearMatch = planName.match(/ปี\s*(\d{4})/);
  return yearMatch?.[1] ?? 'XXXX';
};

// Function to update global store with dynamic breadcrumb
const updateDevelopmentPlanBreadcrumb = (plan: DevelopmentPlan | null) => {
  if (plan) {
    const year = extractYearFromPlanName(plan.name);
    globalStore.setDevelopmentPlanYear(year);
  }
};

// Function to fetch development plan by ID
const fetchDevelopmentPlan = async (id: number) => {
  loading.value = true;
  try {
    const plan = await devPlansService.fetchOne(id);
    developmentPlan.value = plan;
    updateDevelopmentPlanBreadcrumb(plan);
  } catch (error) {
    console.error('Error fetching development plan:', error);
    developmentPlan.value = null;
    updateDevelopmentPlanBreadcrumb(null);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const planId = Number(route.params.id);
  if (planId && !isNaN(planId)) {
    void fetchDevelopmentPlan(planId);
  }
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  (newId) => {
    const planId = Number(newId);
    if (planId && !isNaN(planId)) {
      void fetchDevelopmentPlan(planId);
    }
  },
);

// Clear breadcrumb when component unmounts
onUnmounted(() => {
  globalStore.clearDevelopmentPlanYear();
});
</script>

<style scoped></style>
