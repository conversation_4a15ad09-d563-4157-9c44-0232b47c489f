<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการอายุงาน"
      create-button-label="เพิ่มข้อมูล"
      @search="onSearchUpdate"
      @create="onClickAdd"
      class="justify-end"
    />

    <q-table
      :rows="ageWorkCriteriaStore.ageWorkCriteria"
      :columns="ageWorkManagementColumns"
      :loading="ageWorkCriteriaStore.loading"
      v-model:pagination="tablePagination"
      @request="onRequest"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn dense unelevated class="view-icon" icon="build" @click="onClickPlanning(row)" />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import AgeWorkCriteriaForm from 'src/components/idp/AgeWorkCriteriaForm.vue';
import { useAgeWorkCriteriaStore } from 'src/stores/age_work_criteria';
import type { AgeWorkCriteria } from 'src/types/idp';
import type { QTableProps, QTableColumn } from 'quasar';

const $q = useQuasar();
const $router = useRouter();
const ageWorkCriteriaStore = useAgeWorkCriteriaStore();

// Age work management columns
const ageWorkManagementColumns: QTableColumn[] = [
  {
    name: 'id',
    label: 'รหัส',
    align: 'left',
    field: 'id',
    sortable: true,
    required: true,
  },
  {
    name: 'name',
    label: 'ชื่อเกณฑ์อายุงาน',
    align: 'center',
    field: 'name',
    sortable: true,
    required: true,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center',
    field: '',
    sortable: false,
    required: true,
  },
];

const searchKeyword = ref('');

// Table pagination state
const tablePagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
  sortBy: 'id',
  descending: false,
});

onMounted(() => {
  void loadAgeWorkCriteria();
});

const loadAgeWorkCriteria = async () => {
  try {
    const response = await ageWorkCriteriaStore.fetchAgeWorkCriteria(
      tablePagination.value,
      searchKeyword.value,
    );
    // Update pagination with response data
    tablePagination.value.rowsNumber = response.total;
  } catch (error: unknown) {
    console.error('Error loading age work criteria:', error);
  }
};

const onRequest = async (props: { pagination: QTableProps['pagination'] }) => {
  try {
    const newPagination = props.pagination;

    // Update local pagination state
    if (newPagination) {
      tablePagination.value.page = newPagination.page ?? 1;
      tablePagination.value.rowsPerPage = newPagination.rowsPerPage ?? 10;
      tablePagination.value.sortBy = newPagination.sortBy ?? 'id';
      tablePagination.value.descending = newPagination.descending ?? false;
    }

    const response = await ageWorkCriteriaStore.fetchAgeWorkCriteria(
      props.pagination,
      searchKeyword.value,
    );

    // Update pagination with response data
    tablePagination.value.rowsNumber = response.total;
  } catch (error: unknown) {
    console.error('Error loading age work criteria:', error);
  }
};

const onClickPlanning = (row: AgeWorkCriteria) => {
  void $router.push({
    name: 'idp-age-work',
    params: { criteriaId: row.id },
  });
};

const onClickEdit = (row: AgeWorkCriteria) => {
  $q.dialog({
    component: AgeWorkCriteriaForm,
    componentProps: {
      formData: row,
    },
    persistent: true,
  })
    .onOk((data: Omit<AgeWorkCriteria, 'id'>) => {
      void (async () => {
        try {
          await ageWorkCriteriaStore.updateAgeWorkCriteria(row.id, { name: data.name });
          await loadAgeWorkCriteria();
        } catch (error: unknown) {
          console.error('Error updating age work criteria:', error);
        }
      })();
    })
    .onCancel(() => {})
    .onDismiss(() => {});
};

const onClickDelete = (row: AgeWorkCriteria) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณต้องการลบข้อมูล "${row.name}" ใช่หรือไม่?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void (async () => {
      try {
        await ageWorkCriteriaStore.removeAgeWorkCriteria(row.id);
        await loadAgeWorkCriteria();
      } catch (error: unknown) {
        console.error('Error deleting age work criteria:', error);
      }
    })();
  });
};

const onClickAdd = () => {
  $q.dialog({
    component: AgeWorkCriteriaForm,
    componentProps: {},
    persistent: true,
  })
    .onOk((data: Omit<AgeWorkCriteria, 'id'>) => {
      void (async () => {
        try {
          await ageWorkCriteriaStore.createAgeWorkCriteria({ name: data.name });
          await loadAgeWorkCriteria();
        } catch (error: unknown) {
          console.error('Error creating age work criteria:', error);
        }
      })();
    })
    .onCancel(() => {})
    .onDismiss(() => {});
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
  // Reset to first page when searching
  tablePagination.value.page = 1;
  void loadAgeWorkCriteria();
};
</script>

<style scoped></style>
