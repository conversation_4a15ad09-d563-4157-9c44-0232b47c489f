<template>
  <q-page q-page class="column no-wrap">
    <div class="q-pa-md">
      <MonitorHeader
        title="ติดตามผล"
        :show-create-button="false"
        :show-filter="isIndividualTab"
        :subtitle="selectedTabLabel"
      >
        <template #tab>
          <TabNavigation class="q-mt-sm" :tabs="tabItems" v-model="selectedTab" />
        </template>
      </MonitorHeader>
      <div class="q-mt-md">
        <component :is="currentComponent" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import MonitorHeader from 'src/components/monitor/MonitorHeader.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import monitorOverviewPage from './tabs/monitorOverviewPage.vue';
import monitorIndividualPage from './tabs/monitorIndividualPage.vue';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const tabItems = ref([
  { label: 'ภาพรวม', name: 'monitoring', value: 'monitoring' },
  { label: 'รายบุคคล', name: 'individual', value: 'individual' },
]);

const selectedTab = ref('monitoring');

const selectedTabLabel = computed(() => {
  return tabItems.value.find((item) => item.value === selectedTab.value)?.label ?? '';
});
const isIndividualTab = computed(() => selectedTab.value === 'individual');

const currentComponent = computed(() =>
  selectedTab.value === 'monitoring' ? monitorOverviewPage : monitorIndividualPage,
);

// sync tab <-> hash
watch(
  () => route.hash,
  (hash) => {
    const tab = hash ? hash.substring(1) : 'monitoring';
    selectedTab.value = tab;
  },
  { immediate: true },
);

watch(selectedTab, async (tab) => {
  if (route.hash !== `#${tab}`) {
    await router.replace({ hash: `#${tab}` });
  }
});

onMounted(async () => {
  if (!route.hash) {
    await router.replace({ hash: '#monitoring' });
    selectedTab.value = 'monitoring';
  }
});
</script>

<style scoped></style>
