<template>
  <q-page padding>
    <q-table
      :rows="userData"
      :columns="individualUserColumn"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn flat class="view-icon" icon="visibility" @click="onClickView(row.id)" />
          </div>
        </q-td>
      </template>
    </q-table>
    <div class="text-center q-mt-md text-subtitle1">
      “กด
      <q-icon name="visibility" /> เพื่อดูรายละเอียดเพิ่มเติมของบุคลากรที่เลือกจะแสดงที่ด้านล่างนี้”
    </div>
    <div v-if="isShowUserMonitorData" class="q-mt-md" ref="monitorDetailRef">
      <div class="row q-col-gutter-md items-center justify-center">
        <div class="col-6 flex flex-center">
          <q-card>
            <Radar
              :data="radarChart"
              :options="radarChartOptions"
              width="600"
              height="500"
              class="q-mt-md"
            />
          </q-card>
        </div>
        <!-- UserProfile + Donut Charts -->
        <div class="col-6 flex-center">
          <div class="item-center justify-center">
            <UserProfile />
          </div>
          <div class="row q-col-gutter-md q-pt-md justify-center">
            <div class="col-6">
              <q-card class="q-pa-md">
                <div class="text-subtitle2 q-mb-sm">โครงการที่เข้าร่วม</div>
                <Doughnut
                  :data="donutProgramData"
                  :options="donutOptions"
                  width="300"
                  height="200"
                />
              </q-card>
            </div>
            <div class="col-6">
              <q-card class="q-pa-md">
                <div class="text-subtitle2 q-mb-sm">แผนการพัฒนา</div>
                <Doughnut :data="donutPlanData" :options="donutOptions" width="300" height="200" />
              </q-card>
            </div>
          </div>
        </div>
        <div class="col-12 q-mt-sm">
          <div class="text-subtitle1 q-mb-md">ข้อมูลโครงการ</div>
          <q-table
            :rows="programData"
            :columns="programUserMonitorColumns"
            row-key="id"
            flat
            bordered
            wrap-cells
            separator="cell"
          >
            <template v-slot:body-cell-satisfactionSurvey="{ row }">
              <q-td class="text-center">
                <q-chip v-bind="getSatisfactionSurveyChip(row.satisfactionSurvey)">
                  {{ getSatisfactionSurveyChip(row.satisfactionSurvey.label) }}
                </q-chip>
              </q-td>
            </template>
            <template v-slot:body-cell-evaluationResult="{ row }">
              <q-td class="text-center">
                <q-chip v-bind="getEvaluationResultChip(row.evaluationResult)">
                  {{ getEvaluationResultChip(row.evaluationResult.label) }}
                </q-chip>
              </q-td>
            </template>
          </q-table>
        </div>
        <div class="col-12 q-mt-sm">
          <div class="text-subtitle1 q-mb-md">แผนพัฒนา</div>
          <div style="display: flex; gap: 24px">
            <q-table
              :rows="generalSkillRows"
              :columns="generalSkillColumns"
              row-key="id"
              flat
              bordered
              wrap-cells
              separator="cell"
            />
            <q-table
              :rows="academicSkillRows"
              :columns="academicSkillColumns"
              row-key="id"
              flat
              bordered
              wrap-cells
              separator="cell"
            />
          </div>
        </div>
        <div class="col-12 q-mt-sm">
          <q-btn class="downloadBtn" icon="download" label="ดาวน์โหลด์เอกสาร"></q-btn>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import {
  academicSkillColumns,
  generalSkillColumns,
  individualUserColumn,
  programUserMonitorColumns,
} from 'src/data/table_columns';
import UserProfile from 'src/components/common/UserProfile.vue';
import { Radar, Doughnut } from 'vue-chartjs';
import chartDataLabels from 'chartjs-plugin-datalabels';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement,
  chartDataLabels,
);

const isShowUserMonitorData = ref(false);
const monitorDetailRef = ref<HTMLElement | null>(null);

const onClickView = (id: number) => {
  localStorage.setItem('selectedUserId', id.toString());
  isShowUserMonitorData.value = true;
  setTimeout(() => {
    monitorDetailRef.value?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
};

onMounted(() => {
  const selectedUserId = localStorage.getItem('selectedUserId');
  if (selectedUserId) {
    isShowUserMonitorData.value = true;
  }
});

onUnmounted(() => {
  localStorage.removeItem('selectedUserId');
});

// Sample data
const userData = ref([
  {
    id: 1,
    name: 'ธาร วัฒนกุล',
    department: 'บริหาร',
    position: 'อธิการบดี',
    workAge: '5 ปี',
  },
  {
    id: 2,
    name: 'ชล พิริยกานต์',
    department: 'บริหาร',
    position: 'ผู้ช่วยอธิการบดี',
    workAge: '10 ปี',
  },
  {
    id: 3,
    name: 'ลลิตา มะลิวัน',
    department: 'วิชาการ',
    position: 'อาจารย์',
    workAge: '4 ปี',
  },
  {
    id: 4,
    name: 'กานต์ ธีระกิจ',
    department: 'สนับสนุนวิชาการ',
    position: 'สนับสนุนวิชาการ',
    workAge: '15 ปี',
  },
]);

const programData = ref([
  {
    id: 1,
    programName: 'โครงการอบรมเชิงปฏิบัติการ  หลักสูตรการประเมินค่างาน',
    joinDate: '2025-05-26',
    preTestScore: '25/30',
    postTestScore: '30/30',
    satisfactionSurvey: 'passed',
    evaluationResult: 'pending',
  },
  {
    id: 2,
    programName: 'โครงการคลีนิคให้คำปรึกษาการขอตำแหน่งชำนาญการ',
    joinDate: '2025-05-26',
    preTestScore: '25/30',
    postTestScore: '30/30',
    satisfactionSurvey: 'pending',
    evaluationResult: 'failed',
  },
  {
    id: 3,
    programName: 'โครงการการพัฒนาผู้ประสานงานโครงการวิจัย',
    joinDate: '2025-05-26',
    preTestScore: '25/30',
    postTestScore: '30/30',
    satisfactionSurvey: 'passed',
    evaluationResult: 'passed',
  },
  {
    id: 4,
    programName: 'โครงการให้ความรู้ด้านประกันสังคม',
    joinDate: '2025-05-26',
    preTestScore: '25/30',
    postTestScore: '30/30',
    satisfactionSurvey: 'passed',
    evaluationResult: 'passed',
  },
]);

const generalSkillRows = ref([
  { id: 1, name: 'การอ่าน การตีความ และการใช้กฎระเบียบทั่วไปของมหาวิทยาลัย' },
  { id: 2, name: 'การเขียนหนังสือหรือเอกสารราชการ' },
  { id: 3, name: 'การพัฒนาทักษะภาษาอังกฤษ' },
  { id: 4, name: 'การทำงานเป็นทีม' },
]);
const academicSkillRows = ref([
  { id: 1, name: 'การจัดการเรียนการสอน การให้คำปรึกษา การวัด และประเมินผล' },
  { id: 2, name: 'การพัฒนาสื่อการสอนหรือการจัดการเรียนการสอนรูปแบบใหม' },
  { id: 3, name: 'การบริหารโครงการวิจัยและการให้บริการวิชาการ' },
  { id: 4, name: 'แนวทางการพัฒนาผลงานทางวิชาการ' },
]);

const radarChart = ref({
  labels: [
    'ความคิดสร้างสรรค์และนวัตกรรม',
    'การแสวงหาและถ่ายทอดความรู้',
    'ความใส่ใจพัฒนามหาวิทยาลัย',
    'การพัฒนาอย่างยั่งยืน',
    'การบริหารทรัพยากร',
    'ความรับผิดชอบต่อสังคม',
  ],
  datasets: [
    {
      backgroundColor: 'rgba(255, 206, 86, 0.2)',
      borderColor: 'rgba(255, 206, 86, 1)',
      pointBackgroundColor: 'rgba(255, 206, 86, 1)',
      pointBorderColor: '#fff',
      data: [4, 2, 3, 4, 5, 3],
    },
  ],
});

const donutProgramData = ref({
  labels: ['ผ่าน', 'รอการประเมิน', 'ยังไม่ผ่าน'],
  datasets: [
    {
      data: [12, 5, 3],
      backgroundColor: ['#76DD8F', '#FFDB42', '#FB685F'],
      borderColor: ['#fff', '#fff', '#fff'],
      borderWidth: 2,
    },
  ],
});

const donutPlanData = ref({
  labels: ['อบรมครบแล้ว', 'กำลังดำเนินการ', 'ยังไม่เริ่ม'],
  datasets: [
    {
      data: [8, 6, 4],
      backgroundColor: ['#76DD8F', '#FFDB42', '#FB685F'],
      borderColor: ['#fff', '#fff', '#fff'],
      borderWidth: 2,
    },
  ],
});

const radarChartOptions = ref({
  responsive: false,
  plugins: {
    legend: {
      display: false,
    },
    title: {
      display: false,
    },
  },
  scales: {
    r: {
      ticks: {
        color: '#bbb',
        backdropColor: 'transparent',
        stepSize: 1,
        precision: 0,
      },
    },
  },
});

const donutOptions = ref({
  responsive: false,
  plugins: {
    legend: {
      display: true,
      position: 'right' as const,
      labels: {
        color: '#333',
        font: {
          size: 14,
        },
      },
    },
    title: {
      display: false,
    },
    datalabels: {
      color: '#333',
      font: {
        weight: 'bold' as const,
        size: 13,
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (value: number, ctx: any) {
        const total = ctx.dataset.data.reduce((a: number, b: number) => a + b, 0);
        const percent = ((value / total) * 100).toFixed(0);
        return `${percent}%`;
      },
    },
  },
});

function getSatisfactionSurveyChip(status: string) {
  if (status === 'passed') {
    return {
      label: 'เสร็จสิ้น',
      style: 'background-color: #e2e2ef; border: #3d3c91 solid 1px; color: #3d3c91',
    };
  } else if (status === 'pending') {
    return {
      label: 'ยังไม่ประเมิน',
      style: 'background-color: #f3dee1; border: #ab2433 solid 1px; color: #ab2433',
    };
  }
  return null;
}

function getEvaluationResultChip(result: string) {
  if (result === 'passed') {
    return {
      label: 'ผ่าน',
      style: 'background-color: #dff3e3; border: #25ad45 solid 1px; color: #25ad45',
    };
  } else if (result === 'failed') {
    return {
      label: 'ไม่ผ่าน',
      style: 'background-color: #ffd9d9; border: #ff3b30 solid 1px; color: #ff3b30',
    };
  } else if (result === 'pending') {
    return {
      label: 'รอการประเมิน',
      style: 'background-color: #fff8d9; border: #ffce26 solid 1px; color: #ffce26',
    };
  }
  return null;
}
</script>

<style scoped lang="scss">
.downloadBtn {
  background-color: $primary;
  color: black;
  font-weight: bold;
  width: 100%;
}
</style>
