<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการความรู้และทักษะ"
      create-button-label="เพิ่ม"
      pageType="skill"
      @search="onSearchUpdate"
      :subtitle="selectedTabLabel"
    >
      <template #tab>
        <TabNavigation class="q-mt-sm" :tabs="tabItems" v-model="selectedTab" />
      </template>
    </TopHeaderTable>

    <q-table
      :rows="rows"
      :columns="skillManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              :disable="isDisable"
              @click="
                (onClickEdit(row, `ความรู้และทักษะ${skillStore.selectedTap}`),
                (skillStore.isPreview = true))
              "
            />

            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="edit"
              :disable="userRole == 'kkp' && userDpId !== row.dep_id"
              @click="onClickEdit(row, `แก้ไขความรู้และทักษะ${skillStore.selectedTap}`)"
              style="background-color: #303f9f"
              :class="{ 'disabled-btn-gray': userRole == 'kkp' && userDpId !== row.dep_id }"
            >
              <q-tooltip
                v-if="userRole == 'kkp' && userDpId !== row.dep_id"
                class="bg-red text-white"
                :offset="[10, 10]"
              >
                ไม่สามารถแก้ไขได้
              </q-tooltip>
            </q-btn>

            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="delete"
              :disable="userRole == 'kkp' && userDpId !== row.dep_id"
              @click="onClickDelete(row)"
              style="background-color: #ab2433"
              :class="{ 'disabled-btn-gray': userRole == 'kkp' && userDpId !== row.dep_id }"
            >
              <q-tooltip
                v-if="userRole == 'kkp' && userDpId !== row.dep_id"
                class="bg-red text-white"
                :offset="[10, 10]"
              >
                ไม่สามารถลบได้
              </q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>

  <DeleteConfirmDialog
    v-model="showDeleteDialog"
    :title="'ยืนยันการลบ'"
    :message="'คุณต้องการลบข้อมูลนี้หรือไม่?'"
    @confirm="handleDeleteConfirm"
    @cancel="showDeleteDialog = false"
  />
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { skillManagementColumns } from 'src/data/table_columns';
import type { Skill, User } from 'src/types/models';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import { useSkillStore } from 'src/stores/skills';
import { useAuthStore } from 'src/stores/auth';
import { defaultPaginationValue } from 'src/configs/pagination';
import { useSkillsService } from 'src/services/skills/skills';
import DeleteConfirmDialog from 'src/components/common/DeleteConfirmDialog.vue';

const $q = useQuasar();
const skillStore = useSkillStore();
const authStore = useAuthStore();
const user = ref<User>();
const isDisable = ref(false);
const rows = ref<Skill[]>([]);
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const searchKeyword = ref('');
const tabItems = [
  { label: 'ทั่วไปบุคลากร', value: 'ทั่วไปบุคลากร' },
  { label: 'ทั่วไปผู้บริหาร', value: 'ทั่วไปผู้บริหาร' },
  {
    label: 'เฉพาะด้านสายวิชาการ',
    value: 'เฉพาะด้านสายวิชาการ',
  },
  {
    label: 'เฉพาะด้านสายสนับสนุนวิชาการ',
    value: 'เฉพาะด้านสายสนับสนุนวิชาการ',
  },
  { label: 'เฉพาะด้านผู้บริหาร', value: 'เฉพาะด้านผู้บริหาร' },
];

const TAB_KEY = 'selected-skill-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'general-staff');
const selectedTabLabel = computed(() => {
  return tabItems.find((item) => item.value === selectedTab.value)?.label ?? '';
});

const mockUserKkp: User = {
  id: 101,
  roleId: 2, // This is okay if 2 maps to 'kkp' in your system
  name: 'สมชาย รักชาติ',
  email: '<EMAIL>',
  newPassword: '',
  roles: [
    { id: 1, name: 'Super Admin', description: 'ผู้ดูแลระบบ', userId: 101 },
    { id: 2, name: 'kkp', description: 'ผู้ใช้งาน KKP', userId: 101 }, // Added 'kkp' role
  ],
  psnPermissions: [
    { perId: 201, perNameEn: 'View Reports' },
    { perId: 202, perNameEn: 'Manage Data' },
  ],
  dep_id: 1,
};

const hasKkpRole = mockUserKkp.roles?.some((role) => role.name === 'kkp') || false;
const userRole = hasKkpRole ? 'kkp' : mockUserKkp.roles?.[0]?.name || '';
const userDpId = mockUserKkp.dep_id;

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await useSkillsService.getAll(_pag)
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};
watch(
  () => selectedTab.value,
  async () => {
    skillStore.selectedTap = selectedTab.value;
    await fetchDataRow(defaultPaginationValue).catch((error) => {
      console.error('Failed to fetch assessments:', error);
    });
  },
  { immediate: true },
);

onMounted(() => {
  user.value = authStore.getCurrentUser();
});

// เก็บค่าไว้ทุกครั้งที่เปลี่ยน
watch(selectedTab, (val) => {
  localStorage.setItem(TAB_KEY, val);
});

const onClickEdit = async (row: Skill, action: string) => {
  await handleEditSkill(row, action);
  skillStore.createDialog = true;
};

const handleEditSkill = async (row: Skill, action: string) => {
  skillStore.dialogTitile = action;
  try {
    if (row.id) {
      await skillStore.fetchOne(row.id);
    }
  } catch (error) {
    console.error('Failed to delete skill:', error);
    // คุณอาจแจ้งเตือนผ่าน $q.notify ได้ด้วย
    $q.notify({
      type: 'negative',
      message: 'โหลดข้อมูลไม่สำเร็จ',
    });
  }
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
};

const showDeleteDialog = ref(false);
const rowToDelete = ref<Skill | null>(null);

const onClickDelete = (row: Skill) => {
  rowToDelete.value = row;
  showDeleteDialog.value = true;
};
const handleDeleteConfirm = () => {
  if (!rowToDelete.value || !rowToDelete.value.id) {
    $q.notify({
      type: 'negative',
      message: 'Competency ID is undefined',
      position: 'bottom',
    });
    showDeleteDialog.value = false;
    return;
  }
  skillStore
    .deleteSkill(rowToDelete.value.id)
    .then(async () => fetchDataRow(defaultPaginationValue))
    .then(() => {
      $q.notify({
        type: 'positive',
        message: 'ลบสมรรถนะสำเร็จ',
        position: 'bottom',
      });
    })
    .catch((error) => {
      console.error('Error deleting competency:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการลบสมรรถนะ',
        position: 'bottom',
      });
    })
    .finally(() => {
      showDeleteDialog.value = false;
      rowToDelete.value = null;
    });
};
watch(
  () => skillStore.createDialog,
  async (newVal, oldVal) => {
    if (oldVal === true && newVal === false) {
      await fetchDataRow(defaultPaginationValue);
    }
  },
);
</script>
<style scoped>
.disabled-btn-gray {
  background-color: #a0a0a0 !important;
}

.disabled-btn-gray .q-icon {
  opacity: 0.6;
}
</style>
