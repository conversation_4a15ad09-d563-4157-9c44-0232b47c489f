<template>
  <q-page class="column no-wrap">
    <div class="q-page q-layout-padding q-gutter-y-lg">
      <UserManagementHeader
        :title="dynamicTitle"
        :show-create-button="tabConfig.showCreateButton"
        :createButtonLabel="tabConfig.createButtonLabel"
        @create="handleCreate"
        @search="handleSearch"
        :show-search="tabConfig.showSearch"
        :show-department-filter="tabConfig.showDepartmentFilter"
        :show-role-filter="tabConfig.showRoleFilter"
      >
        <template #tab>
          <div class="row items-center justify-between q-mt-sm">
            <TabNavigation :tabs="tabNavigationItems" v-model="activeTabName" />
          </div>
        </template>
      </UserManagementHeader>
    </div>

    <div v-if="activeTabName" class="col">
      <q-tab-panels v-model="activeTabName" animated class="full-height">
        <q-tab-panel
          v-for="(tab, index) in defaultUmsTabsMenu"
          :key="index"
          :name="tab.name"
          class="q-pa-none full-height"
        >
          <component
            :is="componentMap[tab.name as keyof typeof componentMap]"
            v-if="activeTabName === tab.name"
            :ref="
              (el: Element | ComponentPublicInstance | null) =>
                setComponentRef(tab.name as string, el)
            "
          />
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { defaultUmsTabsMenu } from 'src/data/menu';
import { defineAsyncComponent, computed, ref, watch, type ComponentPublicInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import UserManagementHeader from 'src/components/common/UserManagementHeader.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';

const route = useRoute();
const router = useRouter();
const TAB_KEY = 'user-role-management-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'roles');
const isInitialLoad = ref(true);

// Tab configuration object
const tabConfigurations = {
  users: {
    showCreateButton: false,
    showDepartmentFilter: true,
    showRoleFilter: true,
    showSearch: true,
    title: 'จัดการผู้ใช้งาน',
    createButtonLabel: '',
  },
  roles: {
    showCreateButton: true,
    showDepartmentFilter: false,
    showRoleFilter: false,
    showSearch: true,
    title: 'จัดการบทบาท',
    createButtonLabel: 'เพิ่ม',
  },
} as const;

watch(selectedTab, (val) => {
  localStorage.setItem(TAB_KEY, val);
});

// Convert QTabProps[] to TabItem[] for TabNavigation component
const tabNavigationItems = computed(() =>
  defaultUmsTabsMenu.map((tab) => ({
    label: String(tab.label || ''),
    value: String(tab.name || ''),
  })),
);

const activeTabName = computed({
  get: (): string => {
    // Get tab name by removing '#' from route.hash
    const hashTab = route.hash ? route.hash.substring(1) : '';
    const defaultTab = String(defaultUmsTabsMenu[0]?.name || '');
    return hashTab || selectedTab.value || defaultTab;
  },
  set: (newName: string) => {
    // Update selectedTab and localStorage
    selectedTab.value = newName;
    localStorage.setItem(TAB_KEY, newName);

    // Always update route.hash when user changes tab
    if (newName && route.hash !== `#${newName}`) {
      void router.push({ hash: '#' + newName });
    }
  },
});

// Watch for route hash changes to update selectedTab
watch(
  () => route.hash,
  (newHash) => {
    const tabName = newHash ? newHash.substring(1) : '';

    if (isInitialLoad.value) {
      // On initial load, if no hash present, redirect to the selected tab from localStorage
      if (!newHash && selectedTab.value) {
        void router.replace({ hash: '#' + selectedTab.value });
      } else if (tabName && tabName !== selectedTab.value) {
        selectedTab.value = tabName;
        localStorage.setItem(TAB_KEY, tabName);
      }
      isInitialLoad.value = false;
    } else {
      // Normal operation: update selectedTab when hash changes
      if (tabName && tabName !== selectedTab.value) {
        selectedTab.value = tabName;
        localStorage.setItem(TAB_KEY, tabName);
      }
    }
  },
  { immediate: true },
);

const componentMap = {
  users: defineAsyncComponent(() => import('./tabs/UserManagementView.vue')),
  roles: defineAsyncComponent(() => import('./tabs/RoleManagementView.vue')),
} as const;

// Define the interface for components that have onClickNewRole method
interface ComponentWithNewRole {
  onClickNewRole?: () => void;
  onSearch?: (keyword: string) => void;
}

// Component references
const componentRefs = ref<Record<string, ComponentWithNewRole>>({});

const setComponentRef = (tabName: string, el: Element | ComponentPublicInstance | null) => {
  if (el && ('onClickNewRole' in el || 'onSearch' in el)) {
    componentRefs.value[tabName] = el as ComponentWithNewRole;
  }
};

const handleCreate = () => {
  const currentComponent = componentRefs.value[activeTabName.value];
  if (currentComponent?.onClickNewRole) {
    currentComponent.onClickNewRole();
  }
};

const handleSearch = (keyword: string) => {
  const currentComponent = componentRefs.value[activeTabName.value];
  if (currentComponent?.onSearch) {
    currentComponent.onSearch(keyword);
  }
};

// Get tab configuration based on active tab
const tabConfig = computed(() => {
  const currentTab = activeTabName.value as keyof typeof tabConfigurations;
  return tabConfigurations[currentTab];
});

// Dynamic title based on active tab (now using tabConfig)
const dynamicTitle = computed(() => tabConfig.value.title);
</script>

<style scoped>
.full-height {
  height: 100%;
}

:deep(.q-tab-panel) {
  padding: 0 !important;
}

:deep(.q-page) {
  padding-block: 0 !important; /* ยกเลิก padding บน–ล่าง */
  margin-block: 0 !important; /* ยกเลิก margin บน–ล่าง */
}
</style>
