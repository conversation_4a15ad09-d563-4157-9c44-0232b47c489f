<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 900px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave">
        <q-card-section class="text-h6">{{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row q-gutter-sm items-center justify-end">
            <SearchBar @search="handleSearch" />
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table
            v-model:pagination="pagination"
            :rows="rows"
            :columns="AssignDepartmentsToUserColumns"
            row-key="id"
            separator="cell"
            @request="onRequest"
          >
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-checkbox
                    :model-value="isFacultySelected(props.row.id)"
                    @update:model-value="(value) => toggleFacultySelection(props.row.id, value)"
                  />
                </div>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent, type QTableProps } from 'quasar';
import { useUserService } from 'src/services/ums/userService';
import { smallPaginationValue } from 'src/configs/pagination';
import { type User } from 'src/types/models';
import type { FacultyWithUserStatus } from 'src/types/data';
import { computed, onMounted, ref } from 'vue';
import { AssignDepartmentsToUserColumns } from 'src/data/table_columns';
import SearchBar from 'src/components/SearchBar.vue';

const pagination = ref({ ...smallPaginationValue });
const rows = ref<FacultyWithUserStatus[]>([]);
const searchTerm = ref('');
const selectedFacultyIds = ref<number[]>([]);

const isFacultySelected = (facultyId: number) => {
  return selectedFacultyIds.value.includes(facultyId);
};

const props = defineProps<{
  user?: User;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();
const formRef = ref<typeof QForm>();
const computedTitle = computed(() =>
  props.user ? `จัดการบุคลากร: ${props.user.name}` : 'จัดการบุคลากร',
);

const formData = ref({
  userId: props.user?.id ?? 0,
  faculties: [] as FacultyWithUserStatus[],
});

const onClickSave = async () => {
  try {
    if (!formRef.value) {
      console.error('Form reference is not set');
      return;
    }
    const isValid = await formRef.value.validate();
    if (isValid) {
      const facultyIds = [...selectedFacultyIds.value];
      await useUserService().updateUserFaculties(formData.value.userId, facultyIds);
      onDialogOK({
        ...formData.value,
        faculties: rows.value.filter((faculty) => selectedFacultyIds.value.includes(faculty.id)),
      });
    }
  } catch (error) {
    console.error('Error in onClickSave:', error);
  }
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleFacultySelection = (facultyId: number, isSelected: boolean) => {
  try {
    if (isSelected) {
      if (!selectedFacultyIds.value.includes(facultyId)) {
        selectedFacultyIds.value.push(facultyId);
      }
    } else {
      const index = selectedFacultyIds.value.indexOf(facultyId);
      if (index > -1) {
        selectedFacultyIds.value.splice(index, 1);
      }
    }
  } catch (error) {
    console.error('Error in toggleFacultySelection:', error);
  }
};

const loadFaculties = async () => {
  if (!props.user?.id) return;

  try {
    const response = await useUserService().getFacultiesWithUserStatus(
      props.user.id,
      pagination.value,
      searchTerm.value,
    );
    rows.value = response.data;
    pagination.value.rowsNumber = response.total;
    response.data.forEach((faculty) => {
      if (faculty.isUserInFaculty && !selectedFacultyIds.value.includes(faculty.id)) {
        selectedFacultyIds.value.push(faculty.id);
      }
    });
  } catch (error) {
    console.error('Error loading faculties:', error);
  }
};

const handleSearch = (search: string) => {
  searchTerm.value = search;
  pagination.value.page = 1;
  loadFaculties();
};

const onRequest: QTableProps['onRequest'] = (requestProp) => {
  if (requestProp.pagination) {
    pagination.value = requestProp.pagination;
    loadFaculties();
  }
};

onMounted(async () => {
  if (props.user) {
    const resUser = await useUserService().getUserById(props.user?.id ?? 0);
    formData.value = {
      userId: resUser.id,
      faculties: [],
    };
    await loadFaculties();
  }
});
</script>
