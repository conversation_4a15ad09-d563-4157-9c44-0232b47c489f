<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave">
        <q-card-section class="text-h6">{{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row justify-between items-center">
            <q-select
              v-model="selectedDepartment"
              outlined
              dense
              use-input
              input-debounce="0"
              style="width: 250px"
              label="เลือกส่วนงาน"
              :options="departmentOptions"
              @filter="filterDepartment"
              clearable
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-select>
            <div class="row q-gutter-sm items-center">
              <SearchBar @search="handleSearch" />
            </div>
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table
            v-model:pagination="pagination"
            :rows="rows"
            :columns="AssignRoleToUserColumns"
            row-key="id"
            separator="cell"
          >
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-checkbox
                    v-model="props.row.selected"
                    @update:model-value="toggleUser(props.row)"
                  />
                </div>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { useRoleService } from 'src/services/ums/roleService';
import { useUserService } from 'src/services/ums/userService';
import { defaultPaginationValue } from 'src/configs/pagination';
import type { Role, User } from 'src/types/models';
import { computed, onMounted, ref, watch } from 'vue';
import { AssignRoleToUserColumns } from 'src/data/table_columns';
import SearchBar from 'src/components/SearchBar.vue';
import { updateUserFacultiesApi } from 'src/apis/user';

const pagination = ref({ ...defaultPaginationValue });
// const rows = ref([
//   { id: 1, name: 'ผู้ดูแลระบบสูงสุด', selected: false },
//   { id: 2, name: 'ผู้ดูแลระบบ', selected: false },
//   { id: 3, name: 'ผู้ดูระบบส่วนงาน', selected: false },
//   { id: 4, name: 'หัวหน้า/ผู้บังคับบัญชา', selected: false },
//   { id: 5, name: 'บุคลากร', selected: false },
//   { id: 6, name: 'ผู้บริหาร', selected: false },
//   { id: 7, name: 'ผู้ตรวจสอบหลักฐาน', selected: false },
// ]);

const props = defineProps<{
  user?: User;
}>();

const allDepartments = [
  { label: 'ส่วนงาน A', value: 'A' },
  { label: 'ส่วนงาน B', value: 'B' },
  { label: 'ส่วนงาน C', value: 'C' },
  { label: 'ส่วนงาน D', value: 'D' },
];

const selectedDepartment = ref<string | null>(null);
// Filtered options for display
const departmentOptions = ref(allDepartments);
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();
const computedTitle = computed(() =>
  props.user ? `จัดการบุคลากร: ${props.user.name}` : 'จัดการบุคลากร',
);
const formData = ref({
  ...props.user,
  roles: props.user?.roles ?? [],
});

const roleOptions = ref<Role[]>([]);
const rows = ref<Role[]>([]);

const selectedRoles = ref<Role[]>(props.user?.roles ?? []);
const selectedRoleIds = computed(() => selectedRoles.value.map((role) => role.id));

watch(
  roleOptions,
  (newRoles) => {
    // Map roles to rows with 'selected' property
    rows.value = newRoles.map((role) => ({
      ...role,
      selected: selectedRoleIds.value.includes(role.id),
    }));
  },
  { immediate: true },
);

// Keep rows' selected state in sync with selectedRoles
watch(selectedRoleIds, (ids) => {
  rows.value = rows.value.map((row) => ({
    ...row,
    selected: ids.includes(row.id),
  }));
});

// Sync formData.roles with selectedRoles
watch(
  selectedRoles,
  (newRoles) => {
    formData.value.roles = newRoles;
  },
  { immediate: true },
);

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }

  if (!props.user?.id) {
    console.error('User ID is not set');
    return;
  }

  const isValid = await formRef.value.validate();
  if (isValid) {
    try {
      const roles = selectedRoles.value;
      const roleIds = roles.map((role) => role.id);
      console.log('Selected role IDs:', roleIds);
      console.log('Form data before save:', formData.value);

      // Update user roles via API
      await updateUserFacultiesApi(props.user.id, roleIds);

      // Update formData with the new roles
      formData.value.roles = roles;
     
      // Close dialog and pass updated user data to parent
      onDialogOK({
        ...formData.value,
        id: props.user.id, // Ensure id is included
        roles: roles, // Ensure roles are included
      });
    } catch (error) {
      console.error('Error updating user roles:', error);
      
    }
  }
};
const filterDepartment = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      departmentOptions.value = allDepartments;
    } else {
      const needle = val.toLowerCase();
      departmentOptions.value = allDepartments.filter(
        (option) => option.label.toLowerCase().indexOf(needle) > -1,
      );
    }
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleUser = (user: Record<string, unknown>) => {
  const roleId = user.id as number;
  const isSelected = selectedRoles.value.some((role) => role.id === roleId);
  if (isSelected) {
    selectedRoles.value = selectedRoles.value.filter((role) => role.id !== roleId);
  } else {
    const role = rows.value.find((r) => r.id === roleId);
    if (role) {
      selectedRoles.value.push(role);
    }
  }
  console.log('User toggled:', user);
  console.log('Selected roles before toggle:', selectedRoles.value);
};

// Add a stub for handleSearch to fix the error
const handleSearch = (searchTerm: string) => {
  // Implement search logic here if needed
  console.log('Search term:', searchTerm);
};

onMounted(async () => {
  const resRole = await useRoleService().getRoles({ page: 1, rowsPerPage: 15 });
  roleOptions.value = resRole.data;
  if (props.user) {
    const resUser = await useUserService().getUserById(props.user?.id ?? 0);
    formData.value = { ...resUser, roles: resUser.roles ?? [] };
  }
});
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
