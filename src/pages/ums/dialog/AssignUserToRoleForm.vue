<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row justify-between items-center">
            <q-select
              v-model="selectedDepartment"
              outlined
              dense
              use-input
              input-debounce="0"
              style="width: 250px"
              label="เลือกส่วนงาน"
              :options="departmentOptions"
              @filter="filterDepartment"
              clearable
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-select>
            <div class="row q-gutter-sm items-center">
              <q-select
                v-model="selectedUser"
                outlined
                dense
                use-input
                input-debounce="0"
                clearable
                style="width: 250px"
                label="เลือกบุคลากร"
                :options="userOptions"
                @filter="filterUser"
                :disable="!selectedDepartment"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-select>
            </div>
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table
            v-model:pagination="pagination"
            :rows="selectedDepartment ? rows : []"
            :columns="AssignUserToRoleColumns"
            row-key="id"
            separator="cell"
          >
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-checkbox
                    v-model="props.row.selected"
                    @update:model-value="toggleUser(props.row)"
                  />
                </div>
              </q-td>
            </template>
            <template #no-data>
              <div
                class="text-center q-pa-lg text-grey-6 full-width"
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  min-height: 200px;
                "
              >
                <q-icon name="info" size="2em" class="q-mb-md" />
                <div class="text-body1">กรุณาเลือกส่วนงาน</div>
              </div>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md q-mt-lg">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { defaultPaginationValue } from 'src/configs/pagination';
import type { Role } from 'src/types/models';
import { computed, ref } from 'vue';
import { AssignUserToRoleColumns } from 'src/data/table_columns';

const pagination = ref({ ...defaultPaginationValue });

// Original options data
const allDepartments = [
  { label: 'ส่วนงาน A', value: 'A' },
  { label: 'ส่วนงาน B', value: 'B' },
  { label: 'ส่วนงาน C', value: 'C' },
  { label: 'ส่วนงาน D', value: 'D' },
];

const allUsers = [
  { id: 1, label: 'ผู้ใช้งาน A', value: 'A' },
  { id: 2, label: 'ผู้ใช้งาน B', value: 'B' },
  { id: 3, label: 'ผู้ใช้งาน C', value: 'C' },
  { id: 4, label: 'ผู้ใช้งาน D', value: 'D' },
];

const rows = ref(
  allUsers.map((user) => ({
    id: user.id,
    name: user.label,
    selected: false, // กำหนดค่าเริ่มต้นเป็น false
  })),
);

const props = defineProps<{
  role?: Role;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const selectedDepartment = ref<string | null>(null);
const selectedUser = ref<string | null>(null);

// Filtered options for display
const departmentOptions = ref(allDepartments);
const userOptions = ref(allUsers);

const filterDepartment = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      departmentOptions.value = allDepartments;
    } else {
      const needle = val.toLowerCase();
      departmentOptions.value = allDepartments.filter(
        (option) => option.label.toLowerCase().indexOf(needle) > -1,
      );
    }
  });
};

const filterUser = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      userOptions.value = allUsers;
    } else {
      const needle = val.toLowerCase();
      userOptions.value = allUsers.filter(
        (option) => option.label.toLowerCase().indexOf(needle) > -1,
      );
    }
  });
};

const computedTitle = computed(() => {
  return props.role ? `จัดการบุคลากร - ${props.role.name}` : 'จัดการบุคลากร';
});

const formData = ref({
  ...props.role,
  permissions: props.role?.permissions ?? [],
});

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (!isValid) return;
  onDialogOK({
    ...formData.value,
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleUser = (user: Record<string, unknown>) => {
  console.log('User toggled:', user);
};
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
