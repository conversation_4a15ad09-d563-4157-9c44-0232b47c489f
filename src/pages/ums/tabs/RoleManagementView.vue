<template>
  <q-page class="q-pa-md">
    <q-table
      v-model:pagination="pagination"
      :rows
      :columns="roleColumns"
      row-key="id"
      @request="onRequest"
      separator="cell"
    >
      <template #body-cell-actions="props">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              icon="edit"
              class="edit-graph-icon"
              @click="onClickEdit(props.row as Role)"
            />
            <q-btn
              dense
              unelevated
              icon="supervisor_account"
              class="manage-role-icon"
              @click="onAssignUserToRoleForm(props.row as Role)"
            />
            <q-btn
              dense
              unelevated
              class="manage-perm-icon"
              icon="build"
              @click="onAssignPermToRoleForm(props.row as Role)"
            />
            <q-btn
              dense
              unelevated
              class="del-icon"
              icon="delete"
              @click="onClickDelete(props.row as Role)"
            />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { ascendingPaginationValue } from 'src/configs/pagination';
import { roleColumns } from 'src/data/table_columns';
import { useRoleService } from 'src/services/ums/roleService';
import type { Role } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref } from 'vue';

const pagination = ref({ ...ascendingPaginationValue });
const rows = ref<Role[]>([]);
const searchText = ref<string>('');

const fetchRoles = () => {
  useRoleService()
    .getRoles(pagination.value, searchText.value)
    .then((res) => {
      rows.value = res.data || [];
      pagination.value.rowsNumber = res.total;
    })
    .catch((error) => {
      console.error('Error fetching roles:', error);
    });
};

const onSearch = (keyword: string) => {
  searchText.value = keyword;
  pagination.value.page = 1; // Reset to first page when searching
  fetchRoles();
};

const onRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  pagination.value = { ...pagination.value, ..._pag };
  fetchRoles();
};

onMounted(() => {
  fetchRoles();
});

const $q = useQuasar();

const onClickNewRole = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/CreateRoleForm.vue')),
    persistent: true,
    componentProps: {
      role: null, // Pass null for new role
    },
  }).onOk((payload: Role) => {
    if (payload) {
      useRoleService()
        .createRole(payload)
        .then(() => {
          fetchRoles(); // Refresh the table
          $q.notify({
            type: 'positive',
            message: 'บทบาทถูกสร้างเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error creating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการสร้างบทบาท',
          });
        });
    }
  });
};

// ยังไม่เสร็จสมบูรณ์
const onAssignUserToRoleForm = (role: Role) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignUserToRoleForm.vue')),
    persistent: true,
    componentProps: {
      role,
    },
  }).onOk((payload: Role) => {
    if (payload) {
      useRoleService()
        .updateRole(role.id, payload)
        .then(() => {
          fetchRoles(); // Refresh the table
        })
        .catch((error) => {
          console.error('Error updating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการเพิ่มผู้ใช้งานในบทบาท',
          });
        });
    }
  });
};

// ยังไม่เสร็จสมบูรณ์
const onAssignPermToRoleForm = (role: Role) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignPermToRoleForm.vue')),
    persistent: true,
    componentProps: {
      role,
    },
  }).onOk((payload: Role) => {
    if (payload) {
      useRoleService()
        .updateRole(role.id, payload)
        .then(() => {
          fetchRoles(); // Refresh the table
          $q.notify({
            type: 'positive',
            message: 'เพิ่มสิทธิ์ในบทบาทเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการเพิ่มสิทธิ์ในบทบาท',
          });
        });
    }
  });
};

const onClickEdit = (role: Role) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/CreateRoleForm.vue')),
    persistent: true,
    componentProps: {
      role, // Pass the selected role for editing
    },
  }).onOk((payload: Role) => {
    if (payload) {
      useRoleService()
        .updateRole(role.id, payload)
        .then(() => {
          fetchRoles(); // Refresh the table
          $q.notify({
            type: 'positive',
            message: 'บทบาทถูกแก้ไขเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการแก้ไขบทบาท',
          });
        });
    }
  });
};

const onClickDelete = (role: Role) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณแน่ใจหรือไม่ว่าต้องการลบบทบาท: ${role.name}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    useRoleService()
      .deleteRole(role.id)
      .then(() => {
        fetchRoles(); // Refresh the table
        $q.notify({
          type: 'positive',
          message: 'บทบาทถูกลบเรียบร้อยแล้ว',
        });
      })
      .catch((error) => {
        console.error('Error deleting role:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการลบบทบาท',
        });
      });
  });
};

// Expose the onClickNewRole function to parent component
defineExpose({
  onClickNewRole,
  onSearch,
});
</script>
