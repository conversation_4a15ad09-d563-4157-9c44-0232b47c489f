<template>
  <q-page class="q-pa-md">
    <q-table
      v-model:pagination="pagination"
      :rows
      :columns="userColumns"
      row-key="id"
      @request="onRequest"
      separator="cell"
    >
      <template #body-cell-actions="props">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="manage-role-icon"
              icon="supervisor_account"
              @click="onAssignRoleToUserForm(props.row as User)"
            />
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="business_center"
              @click="onAssignDepartmentsToUserForm(props.row as User)"
            />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { defaultPaginationValue } from 'src/configs/pagination';
import { userColumns } from 'src/data/table_columns';
import { getUsers, useUserService } from 'src/services/ums/userService';
import type { User } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref } from 'vue';

const pagination = ref({ ...defaultPaginationValue });
const rows = ref<User[]>([]);
const onRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  // Ensure sortBy is always a string
  const pag: {
    sortBy: string;
    descending: boolean;
    page: number;
    rowsPerPage: number;
    rowsNumber?: number;
  } = {
    ..._pag,
    sortBy: _pag.sortBy ?? '', // Convert null/undefined to empty string
    descending: _pag.descending ?? false, // Ensure descending is boolean
    page: _pag.page ?? 1, // Default page to 1
    rowsPerPage: _pag.rowsPerPage ?? 10, // Default rowsPerPage
  };
  useUserService()
    .getUsers(pag)
    .then((res) => {
      rows.value = res.data || [];
      pagination.value.rowsNumber = res.total;
    })
    .catch((error) => {
      console.error('Error fetching users:', error);
    });
};

const onSearch = (keyword: string) => {
  pagination.value.page = 1; // Reset to first page when searching
  useUserService()
    .getUsers(defaultPaginationValue, keyword)
    .then((res) => {
      rows.value = res.data || [];
      pagination.value.rowsNumber = res.total;
    })
    .catch((error) => {
      console.error('Error searching users:', error);
    });
};

onMounted(() => {
  useUserService()
    .getUsers(defaultPaginationValue)
    .then((res) => {
      rows.value = res.data || [];
      pagination.value.rowsNumber = res.total;
    })
    .catch((error) => {
      console.error('Error fetching initial users:', error);
    });
});

const $q = useQuasar();
const onAssignRoleToUserForm = (user: User) => {
  // Logic to open the edit user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignRoleToUserForm.vue')),
    persistent: true,
    componentProps: {
      user, // Pass the selected user for editing
    },
  }).onOk((payload: User) => {
    // Logic to handle the updated user data after dialog is closed
    if (payload) {
      useUserService()
        .updateUser(user.id, payload)
        .then(() => {
          getUsers(); // Refresh the table
          $q.notify({
            type: 'positive',
            message: 'กำหนดบทบาทผู้ใช้งานเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating user:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการกำหนดบทบาทผู้ใช้งาน',
          });
        });
    }
  });
};

const onAssignDepartmentsToUserForm = (user: User) => {
  // Logic to open the edit user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignDepartmentsToUserForm.vue')),
    persistent: true,
    componentProps: {
      user, // Pass the selected user for editing
    },
  }).onOk((payload: User) => {
    // Logic to handle the updated user data after dialog is closed
    if (payload) {
      useUserService()
        .updateUser(user.id, payload)
        .then(() => {
          getUsers(defaultPaginationValue)
            .then((res) => {
              rows.value = res.data || [];
              pagination.value.rowsNumber = res.total;
            })
            .catch((error) => {
              console.error('Error fetching updated users:', error);
            });
          $q.notify({
            type: 'positive',
            message: 'กำหนดส่วนงานให้ผู้ใช้งานเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating user:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการกำหนดส่วนงานให้ผู้ใช้งาน',
          });
        });
    }
  });
};

// const onClickDelete = (user: User) => {
//   // Logic to confirm and delete the user
//   $q.dialog({
//     title: 'ยืนยันการลบ',
//     message: `คุณแน่ใจหรือไม่ว่าต้องการลบผู้ใช้งาน: ${user.name}?`,
//     cancel: true,
//     persistent: true,
//   }).onOk(() => {
//     UserService.deleteUser(user.id)
//       .then(() => {
//         rows.value = rows.value.filter((u) => u.id !== user.id); // Remove the user from the table
//         $q.notify({
//           type: 'positive',
//           message: 'ผู้ใช้งานถูกลบเรียบร้อยแล้ว',
//         });
//       })
//       .catch((error) => {
//         console.error('Error deleting user:', error);
//         $q.notify({
//           type: 'negative',
//           message: 'เกิดข้อผิดพลาดในการลบผู้ใช้งาน',
//         });
//       });
//   });
// };

// Expose the onSearch function to parent component
defineExpose({
  onSearch,
});
</script>
