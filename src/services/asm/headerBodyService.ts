import type { Header<PERSON><PERSON> } from 'src/types/models';
import {
  createHeader<PERSON>ody<PERSON><PERSON>,
  fetchAllHeader<PERSON>odies<PERSON><PERSON>,
  fetchHeader<PERSON>ody<PERSON><PERSON>,
  updateHeaderBody<PERSON><PERSON>,
  deleteHeaderBodyApi,
} from 'src/apis/headerBody';
import { getHeaderBodyErrorMessage } from 'src/utils/headerBodyErrors';
import { showSuccess, showError } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

export class HeaderBodyService {
  private readonly path = '/evaluate/header-bodies';

  async createHeaderBody(itemBlockId: number): Promise<HeaderBody> {
    try {
      const data = {
        itemBlockId,
        title: 'แบบฟอร์มไม่มีชื่อ',
        description: 'คำอธิบาย',
      };
      const response = await createHeaderBodyApi(this.path, data);
      showSuccess('สร้างหัวข้อสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getHeaderBodyErrorMessage(axiosError, 'สร้าง');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findAll(): Promise<HeaderBody[]> {
    try {
      const response = await fetchAllHeaderBodiesApi(this.path);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getHeaderBodyErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findOne(id: number): Promise<HeaderBody> {
    try {
      const response = await fetchHeaderBodyApi(this.path, id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getHeaderBodyErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async update(id: number, params: Partial<HeaderBody>): Promise<HeaderBody> {
    try {
      const response = await updateHeaderBodyApi(this.path, id, params);
      showSuccess('อัปเดตหัวข้อสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getHeaderBodyErrorMessage(axiosError, 'อัปเดต');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await deleteHeaderBodyApi(this.path, id);
      showSuccess('ลบหัวข้อสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getHeaderBodyErrorMessage(axiosError, 'ลบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}
