import type { ImageBody } from 'src/types/models';
import {
  createImage<PERSON>ody<PERSON><PERSON>,
  fetchAllImage<PERSON>odiesA<PERSON>,
  fetchImage<PERSON>ody<PERSON><PERSON>,
  updateImageBodyApi,
  deleteImageBodyApi,
} from 'src/apis/imageBody';
import { getImageBodyErrorMessage } from 'src/utils/imageBodyErrors';
import { showSuccess, showError } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

export class ImageBodyService {
  private readonly path = 'image-bodies';

  async createImageBody(params: Partial<ImageBody>, file: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await createImageBodyA<PERSON>(this.path, formData);
      showSuccess('เพิ่มรูปภาพสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'เพิ่ม');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getAllImageBodies(): Promise<ImageBody[]> {
    try {
      const response = await fetchAllImageBodiesApi(this.path);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getImageBodyById(id: number): Promise<ImageBody> {
    try {
      const response = await fetchImageBodyApi(this.path, id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getImageBodyByItemBlockId(itemBlockId: number): Promise<ImageBody | null> {
    try {
      const response = await fetchAllImageBodiesApi(this.path);
      const imageBody = response.data.find((body) => body.itemBlockId === itemBlockId);
      return imageBody || null;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async updateImageBody(id: number, params: Partial<ImageBody>, file?: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await updateImageBodyApi(this.path, id, formData);
      showSuccess('อัปเดตรูปภาพสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'อัปเดต');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async updateImageTextOnly(
    id: number,
    imageText: string,
    existingImagePath?: string,
    existingImageWidth?: number,
    existingImageHeight?: number,
  ): Promise<ImageBody> {
    try {
      const extractRelativePath = (path: string | null | undefined): string | null => {
        if (!path) return null;
        if (path.startsWith('uploaded_files/') && !path.includes('?') && !path.includes('http')) {
          return path;
        }
        if (path.includes('uploaded_files/')) {
          const regex = /uploaded_files\/[^?]+/;
          const match = regex.exec(path);
          return match ? match[0] : null;
        }
        return path;
      };

      const relativeImagePath = extractRelativePath(existingImagePath);

      const formData = new FormData();
      formData.append('imageText', imageText);
      formData.append('imagePath', relativeImagePath || '');
      if (existingImageWidth !== undefined && existingImageWidth !== null) {
        formData.append('imageWidth', JSON.stringify(existingImageWidth));
      }
      if (existingImageHeight !== undefined && existingImageHeight !== null) {
        formData.append('imageHeight', JSON.stringify(existingImageHeight));
      }

      const response = await updateImageBodyApi(this.path, id, formData);
      showSuccess('อัปเดตข้อความรูปภาพสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'อัปเดต');
      showError(errorMessage);
      console.error('❌ updateImageTextOnly failed:', error);
      throw new Error(errorMessage);
    }
  }

  async removeImageBody(id: number): Promise<void> {
    try {
      await deleteImageBodyApi(this.path, id);
      showSuccess('ลบรูปภาพสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getImageBodyErrorMessage(axiosError, 'ลบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (['imageWidth', 'imageHeight', 'itemBlockId'].includes(key)) {
          formData.append(key, JSON.stringify(value));
        } else if (key === 'imageText') {
          formData.append('imageText', value as string);
        } else if (key === 'imagePath' && typeof value === 'string') {
          formData.append('imagePath', value);
        }
      }
    });

    if (file) {
      formData.append('imagePath', file);
    }

    return formData;
  }
}
