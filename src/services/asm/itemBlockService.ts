import type { AxiosError } from 'axios';
import type { ItemBlock } from 'src/types/models';
import {
  getAllItemBlocksByAssessmentApi,
  createItemBlockApi,
  updateItemBlockFieldApi,
  deleteItemBlockApi,
  getItemBlockByIdApi,
  updateItemBlockDimensionsApi,
} from 'src/apis/itemBlock';
import { showError } from 'src/utils/notifications';
import { getItemBlockErrorMessage } from 'src/utils/itemBlockErrors';

export class ItemBlockService {
  private readonly path = 'item-blocks';

  async getAllWhereAsmId(assessmentId: number): Promise<ItemBlock[]> {
    try {
      const response = await getAllItemBlocksByAssessmentApi(this.path, assessmentId);
      return response.data;
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'ดึงข้อมูล ItemBlock');
      console.error('Error fetching item blocks:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async addItemBlock(params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await createItemBlockApi(this.path, params);
      return response.data;
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'เพิ่ม ItemBlock');
      console.error('Error adding item block:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async updateItemBlockField(itemId: number, params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await updateItemBlockFieldApi(this.path, itemId, params);
      return response.data;
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'อัปเดต ItemBlock');
      console.error('Error updating item block field:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async removeItemBlock(itemId: number): Promise<void> {
    try {
      await deleteItemBlockApi(this.path, itemId);
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'ลบ ItemBlock');
      console.error('Error removing item block:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getOne(itemBlockId: number): Promise<ItemBlock> {
    try {
      const response = await getItemBlockByIdApi(this.path, itemBlockId);
      return response.data;
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'โหลด ItemBlock');
      console.error('Error fetching item block:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async updateDimensions(
    itemBlockId: number,
    dimensions: { width: number; height: number },
  ): Promise<ItemBlock> {
    try {
      const response = await updateItemBlockDimensionsApi(this.path, itemBlockId, dimensions);
      return response.data;
    } catch (error) {
      const errorMessage = getItemBlockErrorMessage(error as AxiosError, 'อัปเดตขนาดรูปภาพ');
      console.error('Error updating dimensions:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}
