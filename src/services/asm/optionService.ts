import type { AxiosError } from 'axios';
import type { Option } from 'src/types/models';
import type { CreateOptionData, UpdateOptionData } from 'src/apis/option';
import {
  createOptionApi,
  createOptionForQuestionApi,
  createOptionForQuestionFormDataApi,
  getAllOptionsApi,
  getOptionByIdApi,
  updateOptionApi,
  updateOptionForQuestionApi,
  updateOptionSilentApi,
  findImagePathApi,
  uploadOptionWithFileApi,
  deleteOptionApi,
  deleteFileByOptionTextApi,
} from 'src/apis/option';
import { showSuccess, showError } from 'src/utils/notifications';
import { getOptionErrorMessage } from 'src/utils/optionErrors';

export type { CreateOptionData, UpdateOptionData } from 'src/apis/option';

export class OptionService {
  private readonly path = '/options';

  /**
   * Create option using POST /options with itemBlockId in request body
   * POST /options
   */
  async createOption(data: CreateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const response = await createOptionApi(this.path, data, file);
      showSuccess('สร้างตัวเลือกสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating option:', error);
      console.error('Request data was:', JSON.stringify(data, null, 2));
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Create option for a specific itemBlock (wrapper method)
   * POST /options
   */
  async createOptionForItemBlock(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    // Ensure itemBlockId is included in the request body
    const requestData = {
      ...data,
      itemBlockId: itemBlockId,
    };

    return this.createOption(requestData, file);
  }

  /**
   * Create option for a specific question
   * POST /options/{questionId}
   */
  async createOptionForQuestion(
    questionId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const response = await createOptionForQuestionApi(this.path, questionId, data, file);
      showSuccess('สร้างตัวเลือกสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating option for question:', error);
      console.error('Request data was:', JSON.stringify({ questionId, data }, null, 2));
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Create option for question using form data
   */
  async createOptionForQuestionFormData(questionId: number, formData: FormData): Promise<void> {
    try {
      await createOptionForQuestionFormDataApi(questionId, formData);
    } catch (error) {
      const errorMessage = getOptionErrorMessage(error as AxiosError, 'สร้าง');
      showError(errorMessage);
    }
  }

  /**
   * Update option by optionId
   * PATCH /options/{optionId}
   */
  async updateOption(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const response = await updateOptionApi(this.path, optionId, data, file);
      showSuccess('อัปเดตตัวเลือกสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating option:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Update option for a specific question
   * PATCH /options/{questionId}/{optionId}
   */
  async updateOptionForQuestion(
    questionId: number,
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const response = await updateOptionForQuestionApi(
        this.path,
        questionId,
        optionId,
        data,
        file,
      );
      showSuccess('อัปเดตตัวเลือกสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating option for question:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Keep existing methods for backward compatibility
  async getAllOptions(): Promise<Option[]> {
    try {
      const response = await getAllOptionsApi(this.path);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching all options:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getOptionById(id: number): Promise<Option> {
    try {
      const response = await getOptionByIdApi(this.path, id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching option by ID:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findImagePath(optionText: string): Promise<string> {
    try {
      const response = await findImagePathApi(this.path, optionText);
      return response.data.imagePath;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching imagePath:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async uploadOptionWithFile(option: Option, file?: File): Promise<Option> {
    try {
      const response = await uploadOptionWithFileApi(this.path, option, file);
      showSuccess('อัปโหลดตัวเลือกสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'อัปโหลด');
      console.error('Error uploading option with file:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async removeOption(id: number): Promise<void> {
    try {
      await deleteOptionApi(this.path, id);
      showSuccess('ลบตัวเลือกสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'ลบ');
      console.error('Error removing option:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async removeFileName(optionText: string): Promise<void> {
    try {
      await deleteFileByOptionTextApi(this.path, optionText);
      showSuccess('ลบไฟล์สำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'ลบ');
      console.error('Error removing file:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Validate option data before sending to API
   */
  private validateOptionData(data: CreateOptionData | UpdateOptionData): void {
    if (!data.itemBlockId) {
      throw new Error('itemBlockId is required');
    }

    if (
      'optionText' in data &&
      data.optionText !== undefined &&
      typeof data.optionText !== 'string'
    ) {
      throw new Error('optionText must be a string');
    }

    if (data.value !== undefined && typeof data.value !== 'number') {
      throw new Error('value must be a number');
    }
  }

  /**
   * Create option with validation
   */
  async createOptionWithValidation(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.createOptionForItemBlock(itemBlockId, data, file);
  }

  /**
   * Update option with validation
   */
  async updateOptionWithValidation(
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.updateOption(optionId, data, file);
  }

  /**
   * Update option without notifications (for auto-save)
   * PATCH /options/{optionId}
   */
  async updateOptionSilent(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const response = await updateOptionSilentApi(this.path, optionId, data, file);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getOptionErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating option (silent):', error);
      throw new Error(errorMessage);
    }
  }

  /**
   * Update multiple option sequences after drag-and-drop reordering
   * This method updates each option's sequence individually
   */
  async updateOptionSequences(
    options: Array<{ id: number; sequence: number; optionText: string; value: number }>,
    itemBlockId: number,
  ): Promise<Option[]> {
    try {
      const updatePromises = options.map(async (option) => {
        if (!option.id) {
          // Option without ID, skip
          return null;
        }

        const updateData: UpdateOptionData = {
          itemBlockId,
          optionText: option.optionText,
          value: option.value,
          sequence: option.sequence,
        };

        return await this.updateOptionSilent(option.id, updateData);
      });

      const results = await Promise.all(updatePromises);
      const updatedOptions = results.filter((option): option is Option => option !== null);

      return updatedOptions;
    } catch (error) {
      console.error('❌ [OPTION-SERVICE] Failed to update option sequences:', error);
      throw new Error('Update option sequences failed');
    }
  }
}
