import type { Option, Question } from 'src/types/models';
import { api } from 'src/boot/axios';
import { showError } from 'src/utils/notifications';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();

const createQuestion = async (questionData: Partial<Question>): Promise<{ data: Question }> => {
  try {
    globalStore.Loading();
    const formData = new FormData();
    Object.entries(questionData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });
    return await api.post('/questions', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  } catch {
    showError('ไม่สามารถสร้างคำถามได้');
    throw new Error('Create question failed');
  }
};

const getQuestions = async (quizId?: number): Promise<{ data: Question[] }> => {
  try {
    return await api.get('/questions', { params: { quizId } });
  } catch {
    showError('ไม่สามารถโหลดรายการคำถามได้');
    throw new Error('Get questions failed');
  }
};

const getQuestionById = async (id: number): Promise<{ data: Question }> => {
  try {
    return await api.get(`/questions/${id}`);
  } catch {
    showError('ไม่สามารถโหลดคำถามนี้ได้');
    throw new Error('Get question by ID failed');
  }
};

const deleteQuestion = async (quizId: number, questionId: number): Promise<void> => {
  try {
    await api.delete(`/questions/${questionId}`);
  } catch {
    showError('ไม่สามารถลบคำถามได้');
    throw new Error('Delete question failed');
  }
};

interface QuestionOrder {
  questionId: number;
  sequence: number;
}

const reorderQuestions = async (
  quizId: number,
  questionOrders: QuestionOrder[],
): Promise<{ data: Question[] }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/questions/quiz/${quizId}/reorder`, questionOrders);
  } catch {
    showError('ไม่สามารถจัดเรียงคำถามใหม่ได้');
    throw new Error('Reorder questions failed');
  }
};

const updateQuestion = async (
  id: number,
  question: Partial<Question>,
): Promise<Question | undefined> => {
  try {
    globalStore.Loading();
    const formData = new FormData();
    Object.entries(question).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });
    const res = await api.patch<Question>(`/questions/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res.data;
  } catch {
    showError('ไม่สามารถอัปเดตคำถามได้');
  }
};

const updateQuestionField = async (
  id: number,
  field: string,
  value: unknown,
): Promise<{ data: Question }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/questions/${id}/field`, { field, value });
  } catch {
    showError('ไม่สามารถอัปเดตคำถามได้');
    throw new Error('Update question field failed');
  }
};

const updateChoiceField = async (
  questionId: number,
  choiceId: number,
  field: string,
  value: unknown,
): Promise<{ data: Option }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/questions/${questionId}/choice/${choiceId}`, { field, value });
  } catch {
    showError('ไม่สามารถอัปเดตตัวเลือกได้');
    throw new Error('Update choice field failed');
  }
};

const addChoice = async (
  questionId: number,
  field: string,
  value: unknown,
): Promise<{ data: Option }> => {
  try {
    globalStore.Loading();
    return await api.post(`/questions/${questionId}/choice`, { field, value });
  } catch {
    showError('ไม่สามารถเพิ่มตัวเลือกได้');
    throw new Error('Add choice failed');
  }
};

const removeChoice = async (questionId: number, choiceId: number): Promise<void> => {
  try {
    await api.delete(`/questions/${questionId}/choice/${choiceId}`);
  } catch {
    showError('ไม่สามารถลบตัวเลือกได้');
    throw new Error('Remove choice failed');
  }
};

export default {
  createQuestion,
  getQuestions,
  getQuestionById,
  deleteQuestion,
  reorderQuestions,
  updateQuestion,
  updateQuestionField,
  updateChoiceField,
  addChoice,
  removeChoice,
};
