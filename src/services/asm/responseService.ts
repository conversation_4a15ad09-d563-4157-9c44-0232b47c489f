import type { AxiosError } from 'axios';
import type { Response } from 'src/types/models';
import type { ChartData } from 'src/types/chart';
import {
  createResponseApi,
  getAllResponsesApi,
  getResponseByIdApi,
  findAnswerApi,
  findAnswersApi,
  findRemoveCheckBoxAnswerApi,
  updateResponseApi,
  deleteResponseApi,
  clearAnswerApi,
  getChartDataApi,
  saveUserQuizResponseApi,
  getNumberOfResponsesApi,
  getResponseEvaluationDataApi,
  getResponseHeaderByIdApi,
  exportAssessmentToExcelApi,
} from 'src/apis/response';
import { showError } from 'src/utils/notifications';
import { getResponseErrorMessage } from 'src/utils/responseErrors';

export class ResponsesService {
  private readonly path = '/responses';

  async create(params: Response): Promise<Response> {
    try {
      const response = await createResponseApi(this.path, params);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ส่งคำตอบ');
      console.error('Error creating response:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findAll(): Promise<Response[]> {
    try {
      const response = await getAllResponsesApi(this.path);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลคำตอบ');
      console.error('Error fetching all responses:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findOne(id: number): Promise<Response> {
    try {
      const response = await getResponseByIdApi(this.path, id);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลคำตอบ');
      console.error('Error fetching response:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findAnswer(submissionId: number, questionId: number): Promise<{ data?: Response }> {
    try {
      const response = await findAnswerApi(this.path, submissionId, questionId);
      return response;
    } catch (error: unknown) {
      console.warn('findAnswer fallback to empty object due to error:', error);
      return {};
    }
  }

  async findAnswers(submissionId: number, questionId: number): Promise<Response[]> {
    try {
      const response = await findAnswersApi(this.path, submissionId, questionId);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลคำตอบ');
      console.error('Error fetching answers:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findRemoveCheckBoxAnswer(
    submissionId: number,
    questionId: number,
    selectedOptionId: number,
  ): Promise<Response | null> {
    try {
      const response = await findRemoveCheckBoxAnswerApi(
        this.path,
        submissionId,
        questionId,
        selectedOptionId,
      );
      return response.data;
    } catch (error: unknown) {
      if (error instanceof Error && 'response' in error) {
        const axiosError = error as AxiosError;
        if (axiosError.response?.status === 400) {
          return null;
        }
      }
      console.error('Unexpected error:', error);
      throw error;
    }
  }

  async update(id: number, params: Response): Promise<Response> {
    try {
      const response = await updateResponseApi(this.path, id, params);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'อัปเดตคำตอบ');
      console.error('Error updating response:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await deleteResponseApi(this.path, id);
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ลบคำตอบ');
      console.error('Error removing response:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async clearAnswer(id: number): Promise<void> {
    console.log('service', id);
    try {
      await clearAnswerApi(this.path, id);
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ลบคำตอบ');
      console.error('Error clearing answer:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getChartData(assessmentId: number): Promise<ChartData[]> {
    try {
      const response = await getChartDataApi(this.path, assessmentId);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลกราฟ');
      console.error('Error fetching chart data:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async saveUserQuizResponse(data: Response): Promise<Response> {
    try {
      const response = await saveUserQuizResponseApi(this.path, data);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ส่งคำตอบแบบทดสอบ');
      console.error('Error saving quiz response:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    try {
      const response = await getNumberOfResponsesApi(assessmentId);
      return response.data.number;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงจำนวนคำตอบ');
      console.error('Error fetching number of responses:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getResponseById(id: number): Promise<{ data: ChartData[] }> {
    try {
      const response = await getResponseEvaluationDataApi(id);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลแบบประเมิน');
      console.error('Error fetching response evaluation data:', error);
      showError(errorMessage);
      throw error;
    }
  }

  async getResponseHeaderById(id: number) {
    try {
      const response = await getResponseHeaderByIdApi(id);
      return response.data;
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ดึงข้อมูลหัวข้อคำตอบ');
      console.error('Error fetching response header:', error);
      showError(errorMessage);
      throw error;
    }
  }

  async getAssessmentToExcelById(id: number): Promise<void> {
    try {
      const response = await exportAssessmentToExcelApi(id);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      const contentDisposition = response.headers['content-disposition'];
      let fileName = `assessment_${id}.xlsx`;
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)$/);
        if (fileNameMatch?.[1]) {
          fileName = decodeURIComponent(fileNameMatch[1]);
        }
      }
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage = getResponseErrorMessage(error as AxiosError, 'ส่งออกข้อมูลเป็น Excel');
      console.error('Error exporting to Excel:', error);
      showError(errorMessage);
      throw error;
    }
  }
}
