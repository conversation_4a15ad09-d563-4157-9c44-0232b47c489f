import type { AxiosError } from 'axios';
import type { Submission } from 'src/types/models';
import {
  getSubmissionByAssessmentIdApi,
  getDraftSubmissionApi,
  getAllSubmissionsApi,
  getSubmissionApi,
  createSubmission<PERSON>pi,
  submitSubmissionApi,
  startSubmission<PERSON>pi,
  deleteSubmissionApi,
} from 'src/apis/submission';
import { showSuccess, showError } from 'src/utils/notifications';
import { getSubmissionErrorMessage } from 'src/utils/submissionErrors';

export class SubmissionService {
  private readonly path = '/submissions';

  async getByAssessmentId(assessmentId: number): Promise<Submission> {
    try {
      const response = await getSubmissionByAssessmentIdApi(this.path, assessmentId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching submission by assessment ID:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getDraft(assessmentId: number, userId: number): Promise<Submission> {
    try {
      const response = await getDraftSubmissionApi(this.path, assessmentId, userId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching draft submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getAll(): Promise<Submission[]> {
    try {
      const response = await getAllSubmissionsApi(this.path);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching all submissions:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async fecthOne(id: number): Promise<Submission> {
    try {
      const response = await getSubmissionApi(this.path, id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async create(data: Submission): Promise<Submission> {
    try {
      const payload = {
        ...data,
        endAt: '',
      };
      const response = await createSubmissionApi(this.path, payload);
      showSuccess('สร้างการส่งงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async update(submissionId: number) {
    try {
      const response = await submitSubmissionApi(this.path, submissionId);
      showSuccess('ส่งงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'ส่ง');
      console.error('Error submitting submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await deleteSubmissionApi(this.path, id);
      showSuccess('ลบการส่งงานสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'ลบ');
      console.error('Error deleting submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async startSubmission(linkUrl: string, userId: number): Promise<Submission> {
    try {
      const response = await startSubmissionApi(this.path, linkUrl, userId);
      showSuccess('เริ่มการทำแบบประเมินสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSubmissionErrorMessage(axiosError, 'เริ่ม');
      console.error('Error starting submission:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}

export const summaryService = new SubmissionService();
