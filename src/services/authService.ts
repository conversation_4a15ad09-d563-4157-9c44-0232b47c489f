import { loginBuuApi, loginApi } from 'src/apis/auth';
import type { AxiosResponse, AxiosError } from 'axios';
import type { LoginResponse } from 'src/types/api';
import { showSuccess, showError } from 'src/utils/notifications';

// Error message mapping
const getErrorMessage = (error: AxiosError): string => {
  const status = error.response?.status;

  if (status === 401) {
    return 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง';
  }
  if (status === 429) {
    return 'มีการเข้าสู่ระบบมากเกินไป กรุณาลองใหม่ในภายหลัง';
  }
  if (status && status >= 500) {
    return 'เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ กรุณาลองใหม่ภายหลัง';
  }
  return 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ';
};

/**
 * Service function for BUU login with error handling and notifications
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>> - Resolves on success, throws on error
 */
async function loginBuu(username: string, password: string): Promise<AxiosResponse<LoginResponse>> {
  try {
    const response = await login<PERSON>uu<PERSON><PERSON>(username, password);
    showSuccess('เข้าสู่ระบบสำเร็จ');
    return response;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getErrorMessage(axiosError);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for regular login with error handling and notifications
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>> - Resolves on success, throws on error
 */
async function login(username: string, password: string): Promise<AxiosResponse<LoginResponse>> {
  try {
    const response = await loginApi(username, password);
    showSuccess('เข้าสู่ระบบสำเร็จ');
    return response;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getErrorMessage(axiosError);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Silent login function without notifications (useful for automatic retries)
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>>
 */
async function silentLogin(
  username: string,
  password: string,
): Promise<AxiosResponse<LoginResponse>> {
  return await loginApi(username, password);
}

/**
 * Silent BUU login function without notifications (useful for automatic retries)
 * @param username - User's username
 * @param password - User's password
 * @returns Promise<AxiosResponse<LoginResponse>>
 */
async function silentLoginBuu(
  username: string,
  password: string,
): Promise<AxiosResponse<LoginResponse>> {
  return await loginBuuApi(username, password);
}

export default {
  loginBuu,
  login,
  silentLogin,
  silentLoginBuu,
  // Utilities
  showError,
  showSuccess,
  getErrorMessage,
};
