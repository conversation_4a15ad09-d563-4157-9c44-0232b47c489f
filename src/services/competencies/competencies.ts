import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { Competency } from 'src/types/models';
import type { CompetencyQueryParams } from 'src/types/api';
import {
  getAllCompetenciesApi,
  getCompetency<PERSON>pi,
  getCompetenciesByType<PERSON>pi,
  createCompetency<PERSON>pi,
  updateCompetency<PERSON>pi,
  deleteCompetencyApi,
} from 'src/apis/competencies';
import { showSuccess, showError } from 'src/utils/notifications';
import { getCompetencyErrorMessage } from 'src/utils/competencyErrors';
import { formatParams } from 'src/utils/formatter';

export class CompetenciesService {
  private readonly path = '/competencies';
  private readonly career_type: string | undefined;

  constructor(career_type?: string) {
    this.career_type = career_type;
  }

  async getAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const format = formatParams(pag, search);
      const params: CompetencyQueryParams = {
        limit: format.limit,
        page: format.page,
        sortBy: format.sortBy || 'id',
        order: format.order,
        ...(search && { search }),
        ...(this.career_type && { career_type: this.career_type }),
      };

      const response = await getAllCompetenciesApi(this.path, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching competencies:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async fetchOne(id: number): Promise<Competency> {
    try {
      const response = await getCompetencyApi(this.path, id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching competency:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async fetchByType(career_type: string): Promise<Competency[]> {
    try {
      const response = await getCompetenciesByTypeApi(this.path, career_type);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching competencies by type:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async create(data: Competency): Promise<Competency> {
    try {
      const response = await createCompetencyApi(this.path, data);
      showSuccess('สร้างสมรรถนะสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating competency:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async update(id: number, data: Partial<Competency>) {
    try {
      const response = await updateCompetencyApi(this.path, id, data);
      showSuccess('อัปเดตสมรรถนะสำเร็จ');
      console.log('Update request data:', data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating competency:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await deleteCompetencyApi(this.path, id);
      showSuccess('ลบสมรรถนะสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getCompetencyErrorMessage(axiosError, 'ลบ');
      console.error('Error deleting competency:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}
