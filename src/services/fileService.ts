import {
  uploadFileApi,
  getFileListApi,
  getPublicFileApi,
  getPublicFilesApi,
  deleteFileApi,
  dsPrefixApi,
  getPublicFilePathApi,
} from 'src/apis/file';
import type { AxiosResponse, AxiosError } from 'axios';
import type {
  UploadFileResponse,
  FileListResponse,
  GetPublicFileResponse,
  GetPublicFilesResponse,
  DeleteFileResponse,
  DsPrefixResponse,
  GetPublicFilePathResponse,
} from 'src/types/api';
import { showSuccess, showError } from 'src/utils/notifications';

// Error message mapping for file operations
const getFileErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  if (status === 413) {
    return 'ไฟล์มีขนาดใหญ่เกินไป';
  }
  if (status === 415) {
    return 'ประเภทไฟล์ไม่ได้รับอนุญาต';
  }
  if (status === 403) {
    return 'ไม่มีสิทธิ์ในการดำเนินการกับไฟล์';
  }
  if (status === 404) {
    return 'ไม่พบไฟล์ที่ต้องการ';
  }
  if (status && status >= 500) {
    return 'เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ กรุณาลองใหม่ภายหลัง';
  }

  // Default error messages based on operation
  const defaultMessages: Record<string, string> = {
    upload: 'ไม่สามารถอัปโหลดไฟล์ได้',
    list: 'ไม่สามารถดึงรายการไฟล์ได้',
    get: 'ไม่สามารถดึงไฟล์ได้',
    delete: 'ไม่สามารถลบไฟล์ได้',
    prefix: 'ไม่สามารถโหลด prefix ได้',
  };

  return defaultMessages[operation] || 'เกิดข้อผิดพลาดในการดำเนินการกับไฟล์';
};

/**
 * Service function for file upload with error handling and notifications
 * @param filePath - Target path for the file
 * @param fileName - Name of the file
 * @param fileType - MIME type of the file
 * @param files - Array of files to upload
 * @returns Promise<AxiosResponse<UploadFileResponse>>
 */
async function uploadFile(
  filePath: string,
  fileName: string,
  fileType: string,
  files: File[],
): Promise<AxiosResponse<UploadFileResponse>> {
  try {
    const response = await uploadFileApi(filePath, fileName, fileType, files);
    showSuccess('อัปโหลดไฟล์สำเร็จ');
    return response;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'upload');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for getting file list with error handling
 * @param path - Directory path to list files from
 * @returns Promise<AxiosResponse<FileListResponse>>
 */
async function getFileList(path: string): Promise<AxiosResponse<FileListResponse>> {
  try {
    return await getFileListApi(path);
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'list');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for getting a public file with error handling
 * @param fileName - Name of the file to retrieve
 * @returns Promise<AxiosResponse<GetPublicFileResponse>>
 */
async function getPublicFile(fileName: string): Promise<AxiosResponse<GetPublicFileResponse>> {
  try {
    return await getPublicFileApi(fileName);
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'get');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for getting multiple public files with error handling
 * @param files - Array of file objects with fileName
 * @returns Promise<AxiosResponse<GetPublicFilesResponse>>
 */
async function getPublicFiles(
  files: { fileName: string }[],
): Promise<AxiosResponse<GetPublicFilesResponse>> {
  try {
    return await getPublicFilesApi(files);
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'get');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for deleting a file with success/error notifications
 * @param fileName - Name of the file to delete
 * @returns Promise<AxiosResponse<DeleteFileResponse>>
 */
async function deleteFile(fileName: string): Promise<AxiosResponse<DeleteFileResponse>> {
  try {
    const response = await deleteFileApi(fileName);
    showSuccess('ลบไฟล์เรียบร้อยแล้ว');
    return response;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'delete');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for getting DS prefix with error handling
 * @returns Promise<AxiosResponse<DsPrefixResponse>>
 */
async function dsPrefix(): Promise<AxiosResponse<DsPrefixResponse>> {
  try {
    return await dsPrefixApi();
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'prefix');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Service function for getting public file path with error handling
 * @param imagePath - Path of the image
 * @returns Promise<GetPublicFilePathResponse['data']>
 */
async function getPublicFilePath(imagePath: string): Promise<GetPublicFilePathResponse> {
  try {
    const response = await getPublicFilePathApi(imagePath);
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getFileErrorMessage(axiosError, 'get');
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Silent versions without notifications (useful for background operations)
 */
const silent = {
  uploadFile: uploadFileApi,
  getFileList: getFileListApi,
  getPublicFile: getPublicFileApi,
  getPublicFiles: getPublicFilesApi,
  deleteFile: deleteFileApi,
  dsPrefix: dsPrefixApi,
  getPublicFilePath: getPublicFilePathApi,
};

export default {
  uploadFile,
  getFileList,
  getPublicFile,
  getPublicFiles,
  deleteFile,
  dsPrefix,
  getPublicFilePath,
  // Utilities
  showError,
  showSuccess,
  getFileErrorMessage,
  // Silent operations
  silent,
};
