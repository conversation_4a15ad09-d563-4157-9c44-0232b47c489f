import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { AgeWorkCriteria } from 'src/types/idp';
import type { AgeWorkCriteriaQueryParams, AgeWorkQueryParams } from 'src/types/api';
import {
  getAllAgeWorkCriteriaApi,
  getAgeWorkCriteriaApi,
  createAgeWorkCriteriaApi,
  updateAgeWorkCriteriaApi,
  deleteAgeWorkCriteriaApi,
  getAgeWorksByCriteriaApi,
} from 'src/apis/ageWorkCriteria';
import { showSuccess, showError } from 'src/utils/notifications';
import { getAgeWorkCriteriaErrorMessage } from 'src/utils/ageWorkErrors';
import { formatParams } from 'src/utils/formatter';

export function useAgeWorkCriteriaService() {
  const getAll = async (pag: QTableProps['pagination'], search?: string) => {
    try {
      const format = formatParams(pag, search);
      const params: AgeWorkCriteriaQueryParams = {
        limit: format.limit,
        page: format.page,
        sortBy: format.sortBy || 'id',
        order: format.order,
        ...(search && { search }),
      };

      const response = await getAllAgeWorkCriteriaApi(params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching age work criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const fetchOne = async (id: number): Promise<AgeWorkCriteria> => {
    try {
      const response = await getAgeWorkCriteriaApi(id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching age work criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const create = async (data: Omit<AgeWorkCriteria, 'id'>): Promise<AgeWorkCriteria> => {
    try {
      const response = await createAgeWorkCriteriaApi(data);
      showSuccess('สร้างเกณฑ์อายุงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating age work criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const update = async (id: number, data: Partial<AgeWorkCriteria>) => {
    try {
      const response = await updateAgeWorkCriteriaApi(id, data);
      showSuccess('อัปเดตเกณฑ์อายุงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating age work criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const remove = async (id: number): Promise<void> => {
    try {
      await deleteAgeWorkCriteriaApi(id);
      showSuccess('ลบเกณฑ์อายุงานสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'ลบ');
      console.error('Error deleting age work criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getAgeWorksByCriteria = async (id: number, pag: QTableProps['pagination'], search?: string) => {
    try {
      const format = formatParams(pag, search);
      const params: AgeWorkQueryParams = {
        limit: format.limit,
        page: format.page,
        sortBy: format.sortBy || 'id',
        order: format.order,
        ...(search && { search }),
      };

      const response = await getAgeWorksByCriteriaApi(id, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkCriteriaErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching age works by criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    getAll,
    fetchOne,
    create,
    update,
    remove,
    getAgeWorksByCriteria,
  };
}
