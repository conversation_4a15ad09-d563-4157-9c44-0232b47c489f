import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { AgeWork } from 'src/types/idp';
import type { AgeWorkQueryParams } from 'src/types/api';
import {
  getAgeWorksByCriteriaApi,
  getAgeWorkApi,
  createAgeWorkApi,
  updateAgeWorkApi,
  deleteAgeWorkApi,
} from 'src/apis/ageWork';
import { showSuccess, showError } from 'src/utils/notifications';
import { getAgeWorkErrorMessage } from 'src/utils/ageWorkErrors';
import { formatParams } from 'src/utils/formatter';

export function useAgeWorkService() {
  const getAgeWorksByCriteria = async (criteriaId: number, pag: QTableProps['pagination'], search?: string) => {
    try {
      const format = formatParams(pag, search);
      const params: AgeWorkQueryParams = {
        limit: format.limit,
        page: format.page,
        sortBy: format.sortBy || 'id',
        order: format.order,
        ...(search && { search }),
      };
      const response = await getAgeWorksByCriteriaApi(criteriaId, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching age works by criteria:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const fetchOne = async (id: number): Promise<AgeWork> => {
    try {
      const response = await getAgeWorkApi(id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching age work:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const create = async (data: Omit<AgeWork, 'id'>): Promise<AgeWork> => {
    try {
      const response = await createAgeWorkApi(data);
      showSuccess('สร้างอายุงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating age work:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const update = async (id: number, data: Partial<AgeWork>) => {
    try {
      const response = await updateAgeWorkApi(id, data);
      showSuccess('อัปเดตอายุงานสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating age work:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const remove = async (id: number): Promise<void> => {
    try {
      await deleteAgeWorkApi(id);
      showSuccess('ลบอายุงานสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAgeWorkErrorMessage(axiosError, 'ลบ');
      console.error('Error deleting age work:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    getAgeWorksByCriteria,
    fetchOne,
    create,
    update,
    remove,
  };
}
