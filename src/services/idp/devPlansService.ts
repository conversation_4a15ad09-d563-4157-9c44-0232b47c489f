import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { DevelopmentPlan } from 'src/types/idp';
import type { DevelopmentPlanResponse, DevelopmentPlanQueryParams } from 'src/types/api';
import {
  getAllDevPlansApi,
  getDevPlanApi,
  createDevPlanApi,
  updateDevPlanApi,
  deleteDevPlanApi,
} from 'src/apis/devPlans';
import { showSuccess, showError } from 'src/utils/notifications';
import { getDevPlanErrorMessage } from 'src/utils/devPlanErrors';

export function useDevPlansService() {
  const getAll = async (
    pag: QTableProps['pagination'],
    search?: string,
    isCentral: 'central' | 'department' = 'central',
  ): Promise<DevelopmentPlanResponse> => {
    try {
      const params: DevelopmentPlanQueryParams = {
        limit: pag?.rowsPerPage || 10,
        page: pag?.page || 1,
        sortBy: pag?.sortBy || 'id',
        order: pag?.descending ? 'DESC' : 'ASC',
        isCentral,
        ...(search && { search }),
      };
      const response = await getAllDevPlansApi(params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getDevPlanErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching development plans:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const fetchOne = async (id: number): Promise<DevelopmentPlan> => {
    try {
      const response = await getDevPlanApi(id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getDevPlanErrorMessage(axiosError, 'โหลด');
      console.error('Error fetching development plan:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const create = async (data: Omit<DevelopmentPlan, 'id'>): Promise<DevelopmentPlan> => {
    try {
      const response = await createDevPlanApi(data);
      showSuccess('สร้างแผนพัฒนาสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getDevPlanErrorMessage(axiosError, 'สร้าง');
      console.error('Error creating development plan:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const update = async (id: number, data: Partial<DevelopmentPlan>): Promise<DevelopmentPlan> => {
    try {
      const response = await updateDevPlanApi(id, data);
      showSuccess('อัปเดตแผนพัฒนาสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getDevPlanErrorMessage(axiosError, 'อัปเดต');
      console.error('Error updating development plan:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const remove = async (id: number): Promise<void> => {
    try {
      await deleteDevPlanApi(id);
      showSuccess('ลบแผนพัฒนาสำเร็จ');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getDevPlanErrorMessage(axiosError, 'ลบ');
      console.error('Error deleting development plan:', error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return {
    getAll,
    fetchOne,
    create,
    update,
    remove,
  };
}
