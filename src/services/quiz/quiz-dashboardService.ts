import type {
  AssessmentMeta,
  ChartData,
  QuestionResponseData,
  ParticipantData,
  ParticipantDetails,
} from 'src/types/quiz-dashboard';
import type { DataParams, DataResponse } from 'src/types/data';
import {
  getDashboardMetaApi,
  getQuestionResponsesApi,
  getParticipantsApi,
  getParticipantDetailsApi,
  getParticipantTextFieldGradingApi,
  saveCustomScoreApi,
} from 'src/apis/quizDashboard';
import { getQuizDashboardErrorMessage } from 'src/utils/quizDashboardErrors';
import { showSuccess, showError } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

export class QuizDashboardService {
  private readonly basePath = '/assessments/dashboard/quiz';

  async getDashboardMeta(assessmentId: number): Promise<AssessmentMeta> {
    try {
      const response = await getDashboardMetaApi(this.basePath, assessmentId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(axiosError, 'โหลดข้อมูลเมตา');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Temporarily disabled as backend endpoint is not available
  async getScoreDistribution(asmId: number): Promise<ChartData> {
    const mockData: ChartData = {
      labels: ['0-20', '21-40', '41-60', '61-80', '81-100'],
      datasets: [
        {
          data: [asmId, 0, 0, 0, 0],
          backgroundColor: ['#FF6384', '#FFCE56', '#36A2EB', '#4BC0C0', '#9966FF'],
        },
      ],
    };
    await Promise.resolve(); // Add await to satisfy TypeScript
    return mockData;
  }

  async getQuestionResponses(
    assessmentId: number,
    params: DataParams,
  ): Promise<DataResponse<QuestionResponseData>> {
    try {
      const response = await getQuestionResponsesApi(this.basePath, assessmentId, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(axiosError, 'โหลดคำตอบของคำถาม');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getParticipants(
    assessmentId: number,
    params: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    try {
      const response = await getParticipantsApi(this.basePath, assessmentId, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(axiosError, 'โหลดรายชื่อผู้เข้าสอบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getParticipantDetails(
    participantId: number,
    params?: Partial<DataParams>,
  ): Promise<DataResponse<ParticipantDetails>> {
    try {
      const response = await getParticipantDetailsApi(this.basePath, participantId, params);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(axiosError, 'โหลดข้อมูลผู้เข้าสอบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getParticipantTextFieldGrading(
    participantId: number,
    params?: Partial<DataParams>,
  ): Promise<DataResponse<ParticipantDetails>> {
    try {
      const response = await getParticipantTextFieldGradingApi(
        this.basePath,
        participantId,
        params,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(
        axiosError,
        'โหลดข้อมูลผู้เข้าสอบสำหรับการให้คะแนน',
      );
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async saveCustomScore(
    submissionId: number,
    questionId: number,
    score: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await saveCustomScoreApi(this.basePath, submissionId, { questionId, score });
      showSuccess('บันทึกคะแนนสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizDashboardErrorMessage(axiosError, 'บันทึกคะแนน');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}

export const quizDashboardService = new QuizDashboardService();
