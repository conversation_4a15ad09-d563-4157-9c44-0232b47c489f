import type { Submission, Response, getQuestionMeta } from 'src/types/models';
import type {
  QuizAllResponsesData,
  QuizMetaResponse,
  QuizScore,
  SaveQuizResponseRequest,
  StartQuizRequest,
  QuizHeaderWithSubmissions,
} from 'src/types/quiz';
import {
  getQuizMetaResponseApi,
  getAllQuizResponsesApi,
  startQuiz<PERSON>pi,
  findSubmission<PERSON><PERSON>,
  submitAssessmentApi,
  getQuizScoreApi,
  getQuestionApi,
  saveQuizResponseApi,
  deleteQuizResponseApi,
  getQuizHeaderWithSubmissionsApi,
} from 'src/apis/quiz';
import {
  getQuizErrorMessage,
  getQuizResponseErrorMessage,
  getQuizQuestionErrorMessage,
  getQuizSubmissionErrorMessage,
} from 'src/utils/quizErrors';
import { showSuccess, showError } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

export class QuizService {
  private readonly path = '/submissions';
  private readonly responsePath = '/responses';
  private readonly itemBlockPath = '/item-blocks';

  async getMetaResponse(quizId: number): Promise<QuizMetaResponse> {
    try {
      const response = await getQuizMetaResponseApi(quizId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizErrorMessage(axiosError, 'โหลดข้อมูลเมตา');
      console.error(`Error fetching quiz meta for quizId ${quizId}:`, error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getAllResponses(quizId: number): Promise<QuizAllResponsesData> {
    try {
      const response = await getAllQuizResponsesApi(quizId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizResponseErrorMessage(axiosError, 'โหลดคำตอบทั้งหมด');
      console.error(`Error fetching all responses for quizId ${quizId}:`, error);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async startQuiz(request: StartQuizRequest): Promise<Submission> {
    try {
      const response = await startQuizApi(this.path, request);
      showSuccess('เริ่มทำแบบทดสอบแล้ว');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizSubmissionErrorMessage(axiosError, 'เริ่ม');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getQuestion(submissionId: number, sequence: number): Promise<getQuestionMeta> {
    try {
      const response = await getQuestionApi(this.itemBlockPath, submissionId, sequence);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizQuestionErrorMessage(axiosError);
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async saveQuizResponse(request: SaveQuizResponseRequest): Promise<Response> {
    try {
      const response = await saveQuizResponseApi(this.responsePath, request);
      showSuccess('บันทึกคำตอบแล้ว');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizResponseErrorMessage(axiosError, 'บันทึก');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async deleteQuizResponse(responseId: number): Promise<void> {
    try {
      await deleteQuizResponseApi(this.responsePath, responseId);
      showSuccess('ลบคำตอบแล้ว');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizResponseErrorMessage(axiosError, 'ลบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async findSubmission(linkUrl: string, userId: number): Promise<Submission | undefined> {
    try {
      const response = await findSubmissionApi(this.path, linkUrl, userId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizSubmissionErrorMessage(axiosError, 'ค้นหา');
      console.error(`Error finding submission for linkUrl ${linkUrl} and userId ${userId}:`, error);
      throw new Error(errorMessage);
    }
  }

  async submitAssessment(submissionId: number): Promise<Submission> {
    try {
      const response = await submitAssessmentApi(this.path, submissionId);
      showSuccess('ส่งแบบทดสอบเรียบร้อยแล้ว');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizSubmissionErrorMessage(axiosError, 'ส่ง');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getQuizScore(submissionId: number): Promise<QuizScore> {
    try {
      const response = await getQuizScoreApi(this.path, submissionId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizSubmissionErrorMessage(axiosError, 'โหลดคะแนน');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getQuizHeaderWithSubmissions(
    linkUrl: string,
    userId?: number,
  ): Promise<QuizHeaderWithSubmissions> {
    try {
      const response = await getQuizHeaderWithSubmissionsApi(linkUrl, userId);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getQuizErrorMessage(axiosError, 'โหลดข้อมูล');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }
}

export const quizService = new QuizService();
