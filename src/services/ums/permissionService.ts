import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { DataResponse } from 'src/types/data';
import type { Permission } from 'src/types/models';
import {
  createPermission<PERSON>pi,
  getPermissionByIdApi,
  updatePermission<PERSON><PERSON>,
  deletePermission<PERSON><PERSON>,
  getPermissions<PERSON>pi,
} from 'src/apis/permission';
import { showSuccess, showError } from 'src/utils/notifications';

/**
 * Get error message for permission operations
 */
function getPermissionErrorMessage(error: AxiosError, operation: string = 'ดำเนินการ'): string {
  const status = error.response?.status;
  const serverMessage = (error.response?.data as { message?: string })?.message;

  if (serverMessage) {
    return `${operation}สิทธิ์ไม่สำเร็จ: ${serverMessage}`;
  }

  switch (status) {
    case 400:
      return `${operation}สิทธิ์ไม่สำเร็จ: ข้อมูลที่ส่งมาไม่ถูกต้อง`;
    case 401:
      return `${operation}สิทธิ์ไม่สำเร็จ: ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบใหม่`;
    case 403:
      return `${operation}สิทธิ์ไม่สำเร็จ: ไม่มีสิทธิ์เข้าถึง`;
    case 404:
      return `${operation}สิทธิ์ไม่สำเร็จ: ไม่พบสิทธิ์ที่ต้องการ`;
    case 409:
      return `${operation}สิทธิ์ไม่สำเร็จ: มีสิทธิ์นี้อยู่ในระบบแล้ว`;
    case 422:
      return `${operation}สิทธิ์ไม่สำเร็จ: ข้อมูลไม่ถูกต้องตามที่ระบบกำหนด`;
    case 500:
      return `${operation}สิทธิ์ไม่สำเร็จ: เกิดข้อผิดพลาดภายในระบบ`;
    default:
      return `${operation}สิทธิ์ไม่สำเร็จ: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ`;
  }
}

// Service Functions with Business Logic and Error Handling
export async function createPermission(dto: Permission): Promise<Permission> {
  try {
    const response = await createPermissionApi(dto);
    showSuccess('สร้างสิทธิ์สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getPermissionErrorMessage(error as AxiosError, 'สร้าง');
    console.error('Error creating permission:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getPermissionById(id: number): Promise<Permission> {
  try {
    const response = await getPermissionByIdApi(id);
    return response.data;
  } catch (error) {
    const errorMessage = getPermissionErrorMessage(error as AxiosError, 'ดึงข้อมูล');
    console.error('Error fetching permission:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function updatePermission(id: number, data: Partial<Permission>): Promise<Permission> {
  try {
    const response = await updatePermissionApi(id, data);
    showSuccess('อัปเดตสิทธิ์สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getPermissionErrorMessage(error as AxiosError, 'อัปเดต');
    console.error('Error updating permission:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function deletePermission(id: number): Promise<void> {
  try {
    await deletePermissionApi(id);
    showSuccess('ลบสิทธิ์สำเร็จ');
  } catch (error) {
    const errorMessage = getPermissionErrorMessage(error as AxiosError, 'ลบ');
    console.error('Error deleting permission:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getPermissions(
  pagination: QTableProps['pagination'],
): Promise<DataResponse<Permission>> {
  try {
    const response = await getPermissionsApi(pagination);
    return response.data;
  } catch (error) {
    const errorMessage = getPermissionErrorMessage(error as AxiosError, 'ดึงรายการ');
    console.error('Error fetching permissions:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

// Composable Functions
export function usePermissionService() {
  return {
    createPermission,
    getPermissionById,
    updatePermission,
    deletePermission,
    getPermissions,
    // Also expose raw API functions for advanced usage
    createPermissionApi,
    getPermissionByIdApi,
    updatePermissionApi,
    deletePermissionApi,
    getPermissionsApi,
  };
}
