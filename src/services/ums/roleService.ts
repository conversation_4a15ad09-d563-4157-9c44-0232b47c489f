import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { DataResponse } from 'src/types/data';
import type { Role } from 'src/types/models';
import {
  createRole<PERSON><PERSON>,
  getRoleByIdApi,
  updateRole<PERSON><PERSON>,
  deleteRole<PERSON><PERSON>,
  getRoles<PERSON>pi,
} from 'src/apis/role';
import { showSuccess, showError } from 'src/utils/notifications';

/**
 * Get error message for role operations
 */
function getRoleErrorMessage(error: AxiosError, operation: string = 'ดำเนินการ'): string {
  const status = error.response?.status;
  const serverMessage = (error.response?.data as { message?: string })?.message;

  if (serverMessage) {
    return `${operation}บทบาทไม่สำเร็จ: ${serverMessage}`;
  }

  switch (status) {
    case 400:
      return `${operation}บทบาทไม่สำเร็จ: ข้อมูลที่ส่งมาไม่ถูกต้อง`;
    case 401:
      return `${operation}บทบาทไม่สำเร็จ: ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบใหม่`;
    case 403:
      return `${operation}บทบาทไม่สำเร็จ: ไม่มีสิทธิ์เข้าถึง`;
    case 404:
      return `${operation}บทบาทไม่สำเร็จ: ไม่พบบทบาทที่ต้องการ`;
    case 409:
      return `${operation}บทบาทไม่สำเร็จ: มีบทบาทนี้อยู่ในระบบแล้ว`;
    case 422:
      return `${operation}บทบาทไม่สำเร็จ: ข้อมูลไม่ถูกต้องตามที่ระบบกำหนด`;
    case 500:
      return `${operation}บทบาทไม่สำเร็จ: เกิดข้อผิดพลาดภายในระบบ`;
    default:
      return `${operation}บทบาทไม่สำเร็จ: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ`;
  }
}

// Service Functions with Business Logic and Error Handling
export async function createRole(dto: Role): Promise<Role> {
  try {
    const response = await createRoleApi(dto);
    showSuccess('สร้างบทบาทสำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getRoleErrorMessage(error as AxiosError, 'สร้าง');
    console.error('Error creating role:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getRoleById(id: number): Promise<Role> {
  try {
    const response = await getRoleByIdApi(id);
    return response.data;
  } catch (error) {
    const errorMessage = getRoleErrorMessage(error as AxiosError, 'ดึงข้อมูล');
    console.error('Error fetching role:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function updateRole(id: number, data: Partial<Role>): Promise<Role> {
  try {
    const response = await updateRoleApi(id, data);
    showSuccess('อัปเดตบทบาทสำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getRoleErrorMessage(error as AxiosError, 'อัปเดต');
    console.error('Error updating role:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function deleteRole(id: number): Promise<void> {
  try {
    await deleteRoleApi(id);
    showSuccess('ลบบทบาทสำเร็จ');
  } catch (error) {
    const errorMessage = getRoleErrorMessage(error as AxiosError, 'ลบ');
    console.error('Error deleting role:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getRoles(
  pagination: QTableProps['pagination'],
  search?: string,
): Promise<DataResponse<Role>> {
  try {
    const response = await getRolesApi(pagination, search);
    return response.data;
  } catch (error) {
    const errorMessage = getRoleErrorMessage(error as AxiosError, 'ดึงรายการ');
    console.error('Error fetching roles:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

// Composable Functions
export function useRoleService() {
  return {
    createRole,
    getRoleById,
    updateRole,
    deleteRole,
    getRoles,
    // Also expose raw API functions for advanced usage
    createRoleApi,
    getRoleByIdApi,
    updateRoleApi,
    deleteRoleApi,
    getRolesApi,
  };
}
