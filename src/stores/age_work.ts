import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useAgeWorkService } from 'src/services/idp/ageWorkService';
import type { AgeWork } from 'src/types/idp';
import type { QTableProps } from 'quasar';

export const useAgeWorkStore = defineStore('ageWork', () => {
  const $q = useQuasar();
  const ageWorkService = useAgeWorkService();

  const ageWorks = ref<AgeWork[]>([]);
  const loading = ref(false);
  const currentPage = ref(1);
  const totalRecords = ref(0);

  async function fetchAgeWorksByCriteria(
    criteriaId: number,
    pagination?: QTableProps['pagination'],
    search?: string,
  ) {
    loading.value = true;
    try {
      const res = await ageWorkService.getAgeWorksByCriteria(criteriaId, pagination, search);
      ageWorks.value = res.data;
      totalRecords.value = res.total;
      currentPage.value = res.curPage;
      return res;
    } catch (err: unknown) {
      let message = 'ดึงข้อมูลอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchOneAgeWork(id: number) {
    loading.value = true;
    try {
      const res = await ageWorkService.fetchOne(id);
      return res;
    } catch (err: unknown) {
      let message = 'ดึงข้อมูลอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function createAgeWork(data: Omit<AgeWork, 'id'>) {
    loading.value = true;
    try {
      const res = await ageWorkService.create(data);
      $q.notify({ type: 'positive', message: 'สร้างอายุงานสำเร็จ' });
      return res;
    } catch (err: unknown) {
      let message = 'สร้างอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function updateAgeWork(id: number, data: Partial<AgeWork>) {
    loading.value = true;
    try {
      const res = await ageWorkService.update(id, data);
      const index = ageWorks.value.findIndex((item) => item.id === id);
      if (index !== -1) {
        ageWorks.value[index] = { ...ageWorks.value[index], ...res };
      }
      $q.notify({ type: 'positive', message: 'อัปเดตอายุงานสำเร็จ' });
      return res;
    } catch (err: unknown) {
      let message = 'อัปเดตอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function removeAgeWork(id: number) {
    loading.value = true;
    try {
      await ageWorkService.remove(id);
      ageWorks.value = ageWorks.value.filter((item) => item.id !== id);
      $q.notify({ type: 'positive', message: 'ลบอายุงานสำเร็จ' });
    } catch (err: unknown) {
      let message = 'ลบอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  return {
    ageWorks,
    loading,
    currentPage,
    totalRecords,
    fetchAgeWorksByCriteria,
    fetchOneAgeWork,
    createAgeWork,
    updateAgeWork,
    removeAgeWork,
  };
});
