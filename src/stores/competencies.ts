import { defineStore } from 'pinia';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { CompetenciesService } from 'src/services/competencies/competencies';
import type { Competency } from 'src/types/models';
import { ref } from 'vue';

export const useCompetencyStore = defineStore('competency', () => {
  const loading = ref(false);
  const $q = useQuasar();
  const competencies = ref<Competency[]>([]);

  const competency = ref<Competency>({
    id: 0,
    name: '',
    career_type: 'สมรรถนะหลัก',
    description: '',
  });

  const editedCompetency = ref<Competency>({...competency.value});

  async function fetchCompetencies(
    _pag: QTableProps['pagination'],
    career_type?: string
  ) {
    loading.value = true;
    try {
      const res = await new CompetenciesService(career_type).getAll(_pag);
      competencies.value = res.data;
      console.log('fetchAll (response): ', res);
    } catch (err: unknown) {
      let message = 'โหลด Competencies ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  async function addCompetency(data: Competency) {
    loading.value = true;
    try {
      const res = await new CompetenciesService().create(data);
      competencies.value = [...competencies.value, res];
      console.log('create (response): ', res);
    } catch (err: unknown) {
      let message = 'สร้างสมรรถนะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  async function updateCompetency(id: number, data: Partial<Competency>) {
    loading.value = true;
    try {
      const res = await new CompetenciesService().update(id, data);
      const index = competencies.value.findIndex(item => item.id === id);
    if (index !== -1) {
        competencies.value[index] = res;
      }
      console.log('update (response): ', res);
    } catch (err: unknown) {
      let message = 'แก้ไขสมรรถนะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  async function deleteCompetency(id: number) {
    loading.value = true;
    try {
      await new CompetenciesService().remove(id);
      competencies.value = competencies.value.filter(item => item.id !== id);
      console.log('delete success');
    } catch (err: unknown) {
      let message = 'ลบสมรรถนะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  return {
    // state
    competencies,
    editedCompetency,
    loading,

    // actions
    fetchCompetencies,
    addCompetency,
    updateCompetency,
    deleteCompetency,
  };
});
