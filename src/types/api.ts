// Authentication API Types
export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface TokenValidationResponse {
  valid: boolean;
  expires_at?: string;
  user_id?: number;
}

// File API Types
export interface UploadFileRequest {
  path: string;
  fileName: string;
  fileType: string;
  files: File[];
}

export interface UploadFileResponse {
  success: boolean;
  message: string;
  data?: {
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    uploadedAt: string;
  };
}

export interface FileListRequest {
  path: string;
}

export interface FileListResponse {
  files: FileInfo[];
  totalCount: number;
}

export interface FileInfo {
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  createdAt: string;
  modifiedAt: string;
}

export interface GetPublicFileRequest {
  fileName: string;
}

export interface GetPublicFileResponse {
  fileName: string;
  fileUrl: string;
  fileSize?: number;
  fileType?: string;
}

export interface GetPublicFilesRequest {
  files: { fileName: string }[];
}

export interface GetPublicFilesResponse {
  files: GetPublicFileResponse[];
}

export interface DeleteFileRequest {
  fileName: string;
}

export interface DeleteFileResponse {
  success: boolean;
  message: string;
}

export interface DsPrefixResponse {
  prefix: string;
  environment: string;
}

export interface GetPublicFilePathRequest {
  imagePath: string;
}

export interface GetPublicFilePathResponse {
  publicPath: string;
  fullUrl: string;
}

// System Permission API Types
export interface SystemPermission {
  perId: number;
  permName: string;
  permDesc?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface GetAllSystemPermsParams {
  sortBy: string | null;
  search: { permName: string };
  page: number;
  itemsPerPage: number;
}

export interface GetAllSystemPermsResponse {
  data: SystemPermission[];
  total: number;
  page: number;
  itemsPerPage: number;
}

export interface CreateSystemPermRequest {
  permName: string;
  permDesc?: string;
  isActive?: boolean;
}

export interface UpdateSystemPermRequest extends CreateSystemPermRequest {
  perId: number;
}

export interface SystemPermResponse {
  success: boolean;
  message: string;
  data?: SystemPermission;
}

// Skills API Types
export interface SkillsQueryParams {
  sortBy?: string;
  descending?: boolean;
  page?: number;
  rowsPerPage?: number;
  search?: string;
  career_type: string;
}

export interface CreateSkillRequest {
  name: string;
  description?: string;
  career_type: string;
  competencyIds?: number[];
}

export interface UpdateSkillRequest extends Partial<CreateSkillRequest> {
  id?: never; // Explicitly exclude id from updates
  evaluator?: never;
  evaluatorId?: never;
  program?: never;
  competencies?: never;
}

export interface SkillResponse {
  success: boolean;
  message: string;
  data?: unknown; // Will be properly typed when imported
}

// Re-export domain types for convenience
export type { Skill } from './models';
export type { DataResponse } from './data';

// Assessment API Types (example for future use)
export interface AssessmentListResponse<T = Record<string, unknown>> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
}

export interface CreateAssessmentRequest {
  name: string;
  description?: string;
  type: 'quiz' | 'evaluate';
}

// Generic API Response Types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, unknown>;
}

export interface PaginationParams {
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

import type { Assessment, ItemBlock } from './models';
import type { DevelopmentPlan } from './idp';

// Assessment API Types
export interface UpdateBlockPayload {
  id: number;
  type: string;
  isRequired?: boolean;
  assessmentId?: number;
  options?: Array<{
    id?: number;
    optionText?: string;
    value?: number;
    itemBlockId?: number;
  }>;
}

export interface UpdateSequenceRequest {
  sequences: { id: number; sequence: number }[];
}

export interface UpdateSequenceResponse {
  success: boolean;
  message: string;
  data: { id: number; sequence: number }[];
}

export interface CreateBlockRequest {
  assessmentId: number;
  type: string;
  sequence: number;
  section: string;
  isRequired: boolean;
}

export interface AssessmentFetchResponse {
  assessment: Assessment;
  pagedItemBlocks: ItemBlock[];
}

// Development Plan API Types
export interface DevelopmentPlanResponse {
  data: DevelopmentPlan[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface DevelopmentPlanQueryParams {
  limit: number;
  page: number;
  sortBy: string;
  order: 'ASC' | 'DESC';
  isCentral: 'central' | 'department';
  search?: string;
}

// Age Work Criteria API Types
export interface AgeWorkCriteriaQueryParams {
  limit: number;
  page: number;
  sortBy: string;
  order: 'ASC' | 'DESC';
  search?: string;
}

export interface AgeWorkQueryParams {
  limit: number;
  page: number;
  sortBy: string;
  order: 'ASC' | 'DESC';
  search?: string;
}

// Competency API Types
export interface CompetencyQueryParams {
  limit: number;
  page: number;
  sortBy: string;
  order: 'ASC' | 'DESC';
  search?: string;
  career_type?: string;
}

// Re-export types from models for convenience
export type { User, Assessment, ItemBlock, HeaderBody, Question, Option } from './models';
export type { DevelopmentPlan } from './idp';
