import type { AxiosError } from 'axios';

/**
 * Get error message for age work criteria operations
 */
export function getAgeWorkCriteriaErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}เกณฑ์อายุงาน`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}เกณฑ์อายุงาน`;
    case 403:
      return `ไม่อนุญาตให้${operation}เกณฑ์อายุงาน`;
    case 404:
      return `ไม่พบเกณฑ์อายุงานที่ต้องการ${operation}`;
    case 409:
      return `เกณฑ์อายุงานนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลเกณฑ์อายุงานไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}เกณฑ์อายุงานมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}เกณฑ์อายุงาน`;
    default:
      return `ไม่สามารถ${operation}เกณฑ์อายุงานได้`;
  }
}

/**
 * Get error message for age work operations
 */
export function getAgeWorkErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}อายุงาน`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}อายุงาน`;
    case 403:
      return `ไม่อนุญาตให้${operation}อายุงาน`;
    case 404:
      return `ไม่พบอายุงานที่ต้องการ${operation}`;
    case 409:
      return `อายุงานนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลอายุงานไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}อายุงานมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}อายุงาน`;
    default:
      return `ไม่สามารถ${operation}อายุงานได้`;
  }
}
