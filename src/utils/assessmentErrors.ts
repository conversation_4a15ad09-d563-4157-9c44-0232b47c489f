import type { AxiosError } from 'axios';

export const getAssessmentErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งไม่ถูกต้องสำหรับการ${operation}แบบประเมิน`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}แบบประเมิน`;
    case 404:
      return operation === 'โหลด'
        ? 'ไม่พบแบบประเมินที่ต้องการ'
        : `ไม่พบแบบประเมินสำหรับการ${operation}`;
    case 409:
      return 'แบบประเมินนี้มีข้อมูลที่ขัดแย้ง';
    case 422:
      return 'ข้อมูลแบบประเมินไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}แบบประเมินได้`;
  }
};

export const getItemBlockErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลบล็อกไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}บล็อก`;
    case 404:
      return operation === 'โหลด' ? 'ไม่พบบล็อกที่ต้องการ' : `ไม่พบบล็อกสำหรับการ${operation}`;
    case 409:
      return 'บล็อกนี้มีข้อมูลที่ขัดแย้ง';
    case 422:
      return 'ข้อมูลบล็อกไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}บล็อกได้`;
  }
};

export const getHeaderBodyErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลหัวข้อไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}หัวข้อ`;
    case 404:
      return operation === 'โหลด' ? 'ไม่พบหัวข้อที่ต้องการ' : `ไม่พบหัวข้อสำหรับการ${operation}`;
    case 422:
      return 'ข้อมูลหัวข้อไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}หัวข้อได้`;
  }
};

export const getQuestionErrorMessage = (error: AxiosError): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return 'ข้อมูลคำถามไม่ถูกต้อง';
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return 'คุณไม่มีสิทธิ์ในการแก้ไขคำถาม';
    case 404:
      return 'ไม่พบคำถามที่ต้องการแก้ไข';
    case 422:
      return 'ข้อมูลคำถามไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return 'ไม่สามารถอัปเดตคำถามได้';
  }
};

export const getOptionErrorMessage = (error: AxiosError): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return 'ข้อมูลตัวเลือกไม่ถูกต้อง';
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return 'คุณไม่มีสิทธิ์ในการแก้ไขตัวเลือก';
    case 404:
      return 'ไม่พบตัวเลือกที่ต้องการแก้ไข';
    case 422:
      return 'ข้อมูลตัวเลือกไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return 'ไม่สามารถอัปเดตตัวเลือกได้';
  }
};
