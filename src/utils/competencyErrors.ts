import type { AxiosError } from 'axios';

/**
 * Get error message for competency operations
 */
export function getCompetencyErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}สมรรถนะ`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}สมรรถนะ`;
    case 403:
      return `ไม่อนุญาตให้${operation}สมรรถนะ`;
    case 404:
      return `ไม่พบสมรรถนะที่ต้องการ${operation}`;
    case 409:
      return `สมรรถนะนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลสมรรถนะไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}สมรรถนะมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}สมรรถนะ`;
    default:
      return `ไม่สามารถ${operation}สมรรถนะได้`;
  }
}
