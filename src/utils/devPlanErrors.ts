import type { AxiosError } from 'axios';

export const getDevPlanErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลแผนพัฒนาไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}แผนพัฒนา`;
    case 404:
      return operation === 'โหลด'
        ? 'ไม่พบแผนพัฒนาที่ต้องการ'
        : `ไม่พบแผนพัฒนาสำหรับการ${operation}`;
    case 409:
      return 'แผนพัฒนานี้มีข้อมูลที่ขัดแย้ง';
    case 422:
      return 'ข้อมูลแผนพัฒนาไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}แผนพัฒนาได้`;
  }
};
