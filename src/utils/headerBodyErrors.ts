import type { AxiosError } from 'axios';

export const getHeaderBodyErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลหัวข้อไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}หัวข้อ`;
    case 404:
      return operation === 'โหลด' ? 'ไม่พบหัวข้อที่ต้องการ' : `ไม่พบหัวข้อสำหรับการ${operation}`;
    case 422:
      return 'ข้อมูลหัวข้อไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}หัวข้อได้`;
  }
};
