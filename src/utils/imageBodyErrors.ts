import type { AxiosError } from 'axios';

export const getImageBodyErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลรูปภาพไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}รูปภาพ`;
    case 404:
      return operation === 'โหลด' ? 'ไม่พบรูปภาพที่ต้องการ' : `ไม่พบรูปภาพสำหรับการ${operation}`;
    case 413:
      return 'ไฟล์รูปภาพใหญ่เกินไป';
    case 415:
      return 'รูปแบบไฟล์ไม่ถูกต้อง กรุณาใช้ไฟล์รูปภาพ';
    case 422:
      return 'ข้อมูลรูปภาพไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}รูปภาพได้`;
  }
};
