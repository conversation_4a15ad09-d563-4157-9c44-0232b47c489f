import { Notify } from 'quasar';

export const showSuccess = (message: string) => {
  Notify.create({
    message,
    type: 'positive',
    position: 'bottom',
    timeout: 3000,
  });
};

export const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

export const showWarning = (message: string) => {
  Notify.create({
    message,
    type: 'warning',
    position: 'bottom',
    timeout: 3000,
  });
};

export const showInfo = (message: string) => {
  Notify.create({
    message,
    type: 'info',
    position: 'bottom',
    timeout: 3000,
  });
};

// Convenience functions for common operations
export const showSaveSuccess = () => showSuccess('บันทึกข้อมูลสำเร็จ');
export const showDeleteSuccess = () => showSuccess('ลบข้อมูลสำเร็จ');
export const showUpdateSuccess = () => showSuccess('อัปเดตข้อมูลสำเร็จ');
export const showCreateSuccess = () => showSuccess('สร้างข้อมูลสำเร็จ');

export const showSaveError = () => showError('ไม่สามารถบันทึกข้อมูลได้');
export const showDeleteError = () => showError('ไม่สามารถลบข้อมูลได้');
export const showUpdateError = () => showError('ไม่สามารถอัปเดตข้อมูลได้');
export const showCreateError = () => showError('ไม่สามารถสร้างข้อมูลได้');
export const showLoadError = () => showError('ไม่สามารถโหลดข้อมูลได้');
