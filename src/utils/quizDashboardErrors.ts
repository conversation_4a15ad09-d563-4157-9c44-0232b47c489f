import type { AxiosError } from 'axios';

export const getQuizDashboardErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลแดชบอร์ดไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}แดชบอร์ด`;
    case 404:
      return operation === 'โหลด'
        ? 'ไม่พบข้อมูลแดชบอร์ดที่ต้องการ'
        : `ไม่พบข้อมูลแดชบอร์ดสำหรับการ${operation}`;
    case 422:
      return 'ข้อมูลแดชบอร์ดไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}แดชบอร์ดได้`;
  }
};
