import type { AxiosError } from 'axios';

export const getQuizErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลแบบทดสอบไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}แบบทดสอบ`;
    case 404:
      return operation === 'โหลด'
        ? 'ไม่พบแบบทดสอบที่ต้องการ'
        : `ไม่พบแบบทดสอบสำหรับการ${operation}`;
    case 409:
      return 'แบบทดสอบนี้มีข้อมูลที่ขัดแย้ง';
    case 422:
      return 'ข้อมูลแบบทดสอบไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}แบบทดสอบได้`;
  }
};

export const getQuizResponseErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลคำตอบไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}คำตอบ`;
    case 404:
      return operation === 'โหลด' ? 'ไม่พบคำตอบที่ต้องการ' : `ไม่พบคำตอบสำหรับการ${operation}`;
    case 422:
      return 'ข้อมูลคำตอบไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}คำตอบได้`;
  }
};

export const getQuizQuestionErrorMessage = (error: AxiosError): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return 'ข้อมูลคำถามไม่ถูกต้อง';
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return 'คุณไม่มีสิทธิ์ในการเข้าถึงคำถาม';
    case 404:
      return 'ไม่พบคำถามที่ต้องการ';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return 'ไม่สามารถโหลดคำถามได้';
  }
};

export const getQuizSubmissionErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  switch (status) {
    case 400:
      return `ข้อมูลการส่งงานไม่ถูกต้องสำหรับการ${operation}`;
    case 401:
      return 'กรุณาเข้าสู่ระบบใหม่';
    case 403:
      return `คุณไม่มีสิทธิ์ในการ${operation}งาน`;
    case 404:
      return operation === 'โหลด'
        ? 'ไม่พบการส่งงานที่ต้องการ'
        : `ไม่พบการส่งงานสำหรับการ${operation}`;
    case 409:
      return 'การส่งงานนี้มีข้อมูลที่ขัดแย้ง';
    case 422:
      return 'ข้อมูลการส่งงานไม่ครบถ้วน';
    case 500:
      return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    default:
      return `ไม่สามารถ${operation}งานได้`;
  }
};
