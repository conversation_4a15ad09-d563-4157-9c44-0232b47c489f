import type { AxiosError } from 'axios';

interface ErrorResponse {
  message?: string;
}

/**
 * Get error message for Response operations
 */
export function getResponseErrorMessage(
  error: AxiosError,
  operation: string = 'ดำเนินการ',
): string {
  const status = error.response?.status;
  const serverMessage = (error.response?.data as ErrorResponse)?.message;

  if (serverMessage) {
    return `${operation}ไม่สำเร็จ: ${serverMessage}`;
  }

  switch (status) {
    case 400:
      return `${operation}ไม่สำเร็จ: ข้อมูลที่ส่งมาไม่ถูกต้อง`;
    case 401:
      return `${operation}ไม่สำเร็จ: ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบใหม่`;
    case 403:
      return `${operation}ไม่สำเร็จ: ไม่มีสิทธิ์เข้าถึง`;
    case 404:
      return `${operation}ไม่สำเร็จ: ไม่พบคำตอบที่ต้องการ`;
    case 409:
      return `${operation}ไม่สำเร็จ: ข้อมูลขัดแย้งกับข้อมูลในระบบ`;
    case 422:
      return `${operation}ไม่สำเร็จ: ข้อมูลไม่ถูกต้องตามที่ระบบกำหนด`;
    case 500:
      return `${operation}ไม่สำเร็จ: เกิดข้อผิดพลาดภายในระบบ`;
    default:
      return `${operation}ไม่สำเร็จ: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ`;
  }
}
