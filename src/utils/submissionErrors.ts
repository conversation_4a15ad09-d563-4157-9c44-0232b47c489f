import type { AxiosError } from 'axios';

/**
 * Get error message for submission operations
 */
export function getSubmissionErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}การส่งงาน`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}การส่งงาน`;
    case 403:
      return `ไม่อนุญาตให้${operation}การส่งงาน`;
    case 404:
      return `ไม่พบการส่งงานที่ต้องการ${operation}`;
    case 409:
      return `การส่งงานนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลการส่งงานไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}การส่งงานมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}การส่งงาน`;
    default:
      return `ไม่สามารถ${operation}การส่งงานได้`;
  }
}

/**
 * Get error message for question operations
 */
export function getQuestionErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}คำถาม`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}คำถาม`;
    case 403:
      return `ไม่อนุญาตให้${operation}คำถาม`;
    case 404:
      return `ไม่พบคำถามที่ต้องการ${operation}`;
    case 409:
      return `คำถามนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลคำถามไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}คำถามมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}คำถาม`;
    default:
      return `ไม่สามารถ${operation}คำถามได้`;
  }
}

/**
 * Get error message for option operations
 */
export function getOptionErrorMessage(error: AxiosError, operation: string): string {
  const status = error.response?.status;
  const responseMessage = (error.response?.data as { message?: string })?.message;

  // Use server message if available and in Thai
  if (responseMessage && typeof responseMessage === 'string') {
    return responseMessage;
  }

  // Fallback to status-based messages
  switch (status) {
    case 400:
      return `ข้อมูลที่ส่งมาไม่ถูกต้องสำหรับการ${operation}ตัวเลือก`;
    case 401:
      return `ไม่มีสิทธิ์ในการ${operation}ตัวเลือก`;
    case 403:
      return `ไม่อนุญาตให้${operation}ตัวเลือก`;
    case 404:
      return `ไม่พบตัวเลือกที่ต้องการ${operation}`;
    case 409:
      return `ตัวเลือกนี้มีอยู่แล้วในระบบ`;
    case 422:
      return `ข้อมูลตัวเลือกไม่ถูกต้องตามรูปแบบ`;
    case 429:
      return `มีการ${operation}ตัวเลือกมากเกินไป กรุณาลองใหม่ในภายหลัง`;
    case 500:
    case 502:
    case 503:
    case 504:
      return `เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ในการ${operation}ตัวเลือก`;
    default:
      return `ไม่สามารถ${operation}ตัวเลือกได้`;
  }
}
